<?php

namespace Drupal\bw_user_shortzzz_project\Plugin\rest\resource;

use Drupal\bw_requests\RequestsService;
use Drupal\bw_rest_resources\ShortzzzProjectSerializer;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Drupal\search_api\Query\QueryInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Return the current user's shortzzz projects.
 *
 * @RestResource(
 *   id = "bw_current_user_shortzzz_projects",
 *   label = @Translation("BW: User: Current user shortzzz projects"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/user/shortzzz-projects",
 *   },
 * )
 */
final class CurrentUserShortzzzProjectsResource extends ResourceBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The requests service.
   *
   * @var \Drupal\bw_requests\RequestsService
   */
  private RequestsService $requestsService;

  /**
   * Shortzzz project serializer.
   *
   * @var \Drupal\bw_rest_resources\ShortzzzProjectSerializer
   */
  private ShortzzzProjectSerializer $shortzzzProjectSerializer;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\bw_requests\RequestsService $requests_service
   *   The requests service.
   * @param \Drupal\bw_rest_resources\ShortzzzProjectSerializer $shortzzz_project_serializer
   *   Shortzzz project serializer.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    AccountInterface $current_user,
    RequestsService $requests_service,
    ShortzzzProjectSerializer $shortzzz_project_serializer,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
    $this->requestsService = $requests_service;
    $this->shortzzzProjectSerializer = $shortzzz_project_serializer;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('bw_requests'),
      $container->get('bw_rest_resources.serializer.shortzzz_project'),
    );
  }

  /**
   * Get the current user's shortzzz projects.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function get(): ModifiedResourceResponse {
    $data = [];

    /** @var \Drupal\search_api\Entity\Index $index */
    $index = $this->entityTypeManager->getStorage('search_api_index')->load('default');
    $query = $index->query()
      ->addCondition('search_api_datasource', 'entity:shortzzz_project')
      ->addCondition('uspj_user_id', $this->currentUser->id())
      ->addCondition('uspj_status', 'banned', '<>')
      ->sort('uspj_id', QueryInterface::SORT_DESC);

    foreach ($query->execute()->getResultItems() as $item) {
      $id = $item->getField('uspj_id')->getValues()[0];
      $status = $item->getField('uspj_status')->getValues()[0];

      if ($id && $status) {
        if (in_array($status, ['approved', 'featured'], TRUE)) {
          $data[] = "/api/v2/shortzzz-project/{$id}";
        }
        elseif (in_array($status, ['pending', 'declined'], TRUE)) {
          $shortzzz_project = $this->entityTypeManager
            ->getStorage('shortzzz_project')
            ->load($id);
          if ($shortzzz_project) {
            $data[] = $this->shortzzzProjectSerializer->deserialize($shortzzz_project);
          }
        }
      }
    }

    $urls = array_filter($data, fn ($datum) => is_string($datum));
    $results_request = !empty($urls)
      ? $this->requestsService->send($urls)
      : [];

    $filtered_data = [];
    foreach ($data as $datum) {
      if (is_array($datum)) {
        $filtered_data[] = $datum;
      }
      elseif (!empty($results_request[$datum])) {
        $filtered_data[] = $results_request[$datum];
      }
    }

    return new ModifiedResourceResponse($filtered_data, 200);
  }

}
