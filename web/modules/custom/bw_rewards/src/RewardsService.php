<?php

namespace Dr<PERSON>al\bw_rewards;

use <PERSON><PERSON><PERSON>\bw_token\Entity\Token;
use <PERSON><PERSON><PERSON>\bw_trophies\Entity\Trophy;
use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\paragraphs\ParagraphInterface;
use <PERSON><PERSON><PERSON>\profile\Entity\Profile;

/**
 * Service for handling rewards.
 */
final class RewardsService {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * Constructs a new RewardsService.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager) {
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * Awards rewards for completing a reward component.
   *
   * @param \Drupal\paragraphs\ParagraphInterface $reward_component
   *   The reward component containing the reward information.
   * @param int $user_id
   *   The user ID to grant the awards to.
   * @param \Drupal\profile\Entity\Profile|null $student_profile
   *   The student profile (optional, otherwise loaded by user ID).
   * @param bool $auto_save
   *   Whether to automatically save the profile (default: false).
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public function awardRewardComponentRewards(ParagraphInterface $reward_component, int $user_id, ?Profile $student_profile = NULL, bool $auto_save = FALSE): bool {
    if (!$student_profile) {
      $student_profile = $this->entityTypeManager->getStorage('profile')
        ->loadByProperties([
          'type' => 'student',
          'uid' => $user_id,
        ]);
      $student_profile = reset($student_profile);

      if (!$student_profile) {
        return FALSE;
      }

      $auto_save = TRUE;
    }

    $updated = FALSE;
    $updated = $this->awardRewardComponentSkills($reward_component, $student_profile) || $updated;
    $updated = $this->awardRewardComponentResources($reward_component, $student_profile) || $updated;

    // Save profile if any changes were made and auto_save is enabled.
    if ($updated && $auto_save) {
      $student_profile->save();
    }

    $this->awardRewardComponentTrophy($reward_component, $user_id);
    $this->awardRewardComponentToken($reward_component, $user_id);

    return $updated;
  }

  /**
   * Awards rewards for a completed node.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node containing reward information.
   * @param int $user_id
   *   The user ID to grant the awards to.
   * @param \Drupal\profile\Entity\Profile|null $student_profile
   *   The student profile (optional, otherwise loaded by user ID).
   * @param bool $auto_save
   *   Whether to automatically save the profile (default: false).
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public function awardNodeCompletionRewards(NodeInterface $node, int $user_id, ?Profile $student_profile = NULL, bool $auto_save = FALSE): bool {
    if (!$student_profile) {
      $student_profile = $this->entityTypeManager->getStorage('profile')
        ->loadByProperties([
          'type' => 'student',
          'uid' => $user_id,
        ]);
      /** @var \Drupal\profile\Entity\Profile $student_profile */
      $student_profile = reset($student_profile);

      if (!$student_profile) {
        return FALSE;
      }

      $auto_save = TRUE;
    }

    $updated = FALSE;
    $updated = $this->awardNodeCompletionSkills($node, $student_profile) || $updated;
    $updated = $this->awardNodeCompletionResources($node, $student_profile) || $updated;

    if ($node->bundle() === 'shortzzz') {
      $save_nano_framework_progress = FALSE;
      /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress $nano_framework_progress */
      $nano_framework_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->loadByProperties([
        'uid' => $user_id,
        'nbid' => $node->id(),
      ]);
      $nano_framework_progress = reset($nano_framework_progress);
      $existing_components_data = $nano_framework_progress->get('data')->value ?? [];

      // Grant rewards for any incomplete reward components.
      foreach ($node->field_components->referencedEntities() as $component) {
        if ($component->bundle() === 'reward' && empty($existing_components_data[$component->uuid()]['completed'])) {
          $updated = $this->awardRewardComponentRewards($component, $user_id, $student_profile, FALSE) || $updated;

          // Update the specific component.
          $existing_components_data[$component->uuid()] = [
            'uuid' => $component->uuid(),
            'completed' => TRUE,
            'data' => [],
          ];

          // Save the updated components data.
          $nano_framework_progress->set('data', ['value' => $existing_components_data]);
          $save_nano_framework_progress = TRUE;
        }
      }

      // Save NFR progress if any changes were made.
      if ($save_nano_framework_progress) {
        $nano_framework_progress->save();
      }
    }
    elseif ($node->bundle() === 'onboarding') {
      // Grant rewards for all reward components.
      foreach ($node->field_components->referencedEntities() as $component) {
        if ($component->bundle() === 'reward') {
          $updated = $this->awardRewardComponentRewards($component, $user_id, $student_profile, FALSE) || $updated;
        }
      }
    }

    // Save profile if any changes were made and auto_save is enabled.
    if ($updated && $auto_save) {
      $student_profile->save();
    }

    $this->awardNodeCompletionTrophy($node, $user_id);
    $this->awardNodeCompletionToken($node, $user_id);

    return $updated;
  }

  /**
   * Awards skills and braynbits from a node.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node containing skills information.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardNodeCompletionSkills(NodeInterface $node, Profile $student_profile): bool {
    if ($node->bundle() !== 'shortzzz') {
      return FALSE;
    }

    $skills = $node->field_sz_skills->referencedEntities();
    if (empty($skills)) {
      return FALSE;
    }

    return $this->awardSkills($skills, $student_profile);
  }

  /**
   * Awards skills and braynbits from a reward component.
   *
   * @param \Drupal\paragraphs\ParagraphInterface $reward_component
   *   The reward component containing skills information.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardRewardComponentSkills(ParagraphInterface $reward_component, Profile $student_profile): bool {
    /** @var \Drupal\paragraphs\Entity\Paragraph[] $skills */
    $skills = $reward_component->field_skills->referencedEntities();
    if (empty($skills)) {
      return FALSE;
    }

    return $this->awardSkills($skills, $student_profile);
  }

  /**
   * Awards skills and braynbits to student profile.
   *
   * @param \Drupal\paragraphs\Entity\Paragraph[] $skills
   *   The skill to be awarded.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardSkills(array $skills, Profile $student_profile): bool {
    $profile_skills = $student_profile->field_s_skills->referencedEntities();
    $profile_skills_map = [];
    foreach ($profile_skills as $profile_skill) {
      $profile_skills_map[$profile_skill->field_ss_skill->value] = $profile_skill;
    }

    $paragraph_storage = $this->entityTypeManager->getStorage('paragraph');
    $profile_modified = FALSE;

    foreach ($skills as $skill) {
      $skill_name = $skill->field_sc_skill->value;
      $brayn_bits = (int) $skill->field_sc_braynbits->value;

      if (isset($profile_skills_map[$skill_name])) {
        // Update existing skill.
        $profile_skill = $profile_skills_map[$skill_name];
        $new_value = $brayn_bits + (int) $profile_skill->field_ss_braynbits->value;
        $profile_skill->set('field_ss_braynbits', $new_value);
      }
      else {
        // Create new skill.
        $profile_skill = $paragraph_storage->create([
          'type' => 'skill_student',
          'field_ss_braynbits' => $brayn_bits,
          'field_ss_skill' => $skill_name,
        ]);
        $student_profile->field_s_skills[] = $profile_skill;
      }

      $profile_skill->save();
      $profile_modified = TRUE;
    }

    return $profile_modified;
  }

  /**
   * Awards rewards (neurons, axons) from a node.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node containing rewards information.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   */
  protected function awardNodeCompletionResources(NodeInterface $node, Profile $student_profile): bool {
    $reward_fields = [
      'field_rewards_neurons' => 'field_s_neurons',
      'field_rewards_axon' => 'field_s_axons',
    ];

    $profile_modified = FALSE;

    foreach ($reward_fields as $node_field => $profile_field) {
      $reward = $node->get($node_field)->value ?? 0;
      if ($reward > 0) {
        $current_value = $student_profile->get($profile_field)->value ?? 0;
        $student_profile->set($profile_field, $current_value + $reward);
        $profile_modified = TRUE;
      }
    }

    return $profile_modified;
  }

  /**
   * Awards rewards (neurons, axons) from a reward component.
   *
   * @param \Drupal\paragraphs\ParagraphInterface $current_component
   *   The reward component containing rewards information.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   */
  protected function awardRewardComponentResources(ParagraphInterface $current_component, Profile $student_profile): bool {
    $reward_fields = [
      'field_neurons' => 'field_s_neurons',
      'field_axons' => 'field_s_axons',
    ];

    $profile_modified = FALSE;

    foreach ($reward_fields as $reward_field => $profile_field) {
      $reward = $current_component->get($reward_field)->value ?? 0;
      if ($reward > 0) {
        $current_value = $student_profile->get($profile_field)->value ?? 0;
        $student_profile->set($profile_field, $current_value + $reward);
        $profile_modified = TRUE;
      }
    }

    return $profile_modified;
  }

  /**
   * Awards a trophy if it exists and is not already awarded.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node containing trophy information.
   * @param int $user_id
   *   The user ID to award the trophy to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardNodeCompletionTrophy(NodeInterface $node, int $user_id): void {
    if ($node->bundle() !== 'shortzzz') {
      return;
    }

    if (!($trophy = $node->field_sz_trophy?->entity) instanceof Trophy) {
      return;
    }

    $this->awardTrophy($trophy, $user_id);
  }

  /**
   * Awards a trophy if it exists and is not already awarded.
   *
   * @param \Drupal\paragraphs\ParagraphInterface $reward_component
   *   The reward component containing trophy information.
   * @param int $user_id
   *   The user ID to award the trophy to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardRewardComponentTrophy(ParagraphInterface $reward_component, int $user_id): void {
    if (!($trophy = $reward_component->field_trophy?->entity) instanceof Trophy) {
      return;
    }

    $this->awardTrophy($trophy, $user_id);
  }

  /**
   * Awards a trophy if it exists and is not already awarded.
   *
   * @param \Drupal\bw_trophies\Entity\Trophy $trophy
   *   The trophy to be awarded.
   * @param int $user_id
   *   The user ID to award the trophy to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardTrophy(Trophy $trophy, int $user_id): void {
    $user_trophy_storage = $this->entityTypeManager->getStorage('user_trophy');
    $user_trophies = $user_trophy_storage->loadByProperties([
      'uid' => $user_id,
      'trophy' => $trophy->id(),
    ]);

    if (empty($user_trophies)) {
      $user_trophy_storage->create([
        'uid' => $user_id,
        'trophy' => $trophy->id(),
        'date_acquired' => (new \DateTime())->getTimestamp(),
      ])->save();
    }
  }

  /**
   * Awards a token if it exists and is not already awarded.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node containing token information.
   * @param int $user_id
   *   The user ID to award the token to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardNodeCompletionToken(NodeInterface $node, int $user_id): void {
    if ($node->bundle() !== 'shortzzz') {
      return;
    }

    if (!($token = $node->field_sz_token?->entity) instanceof Token) {
      return;
    }

    $this->awardToken($token, $user_id);
  }

  /**
   * Awards a token if it exists and is not already awarded.
   *
   * @param \Drupal\paragraphs\ParagraphInterface $reward_component
   *   The reward component containing token information.
   * @param int $user_id
   *   The user ID to award the token to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardRewardComponentToken(ParagraphInterface $reward_component, int $user_id): void {
    if (!($token = $reward_component->field_token?->entity) instanceof Token) {
      return;
    }

    $this->awardToken($token, $user_id);
  }

  /**
   * Awards a token if it exists and is not already awarded.
   *
   * @param \Drupal\bw_token\Entity\Token $token
   *   The token to be awarded.
   * @param int $user_id
   *   The user ID to award the token to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardToken(Token $token, int $user_id): void {
    $user_token_storage = $this->entityTypeManager->getStorage('user_token');
    $user_tokens = $user_token_storage->loadByProperties([
      'uid' => $user_id,
      'token' => $token->id(),
    ]);

    if (empty($user_tokens)) {
      $user_token_storage->create([
        'uid' => $user_id,
        'token' => $token->id(),
        'date_acquired' => (new \DateTime())->getTimestamp(),
      ])->save();
    }
  }

}
