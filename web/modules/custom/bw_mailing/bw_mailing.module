<?php

/**
 * @file
 * Module-related hooks.
 */

use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\Core\Url;

/**
 * Implements hook_theme().
 */
function bw_mailing_theme() {
  return [
    'mimemail_message__bw_mailing' => [
      'render element' => 'elements',
      'base hook' => 'mimemail_message',
    ],
  ];
}

/**
 * Prepares variables for mimemail_message template.
 *
 * @param array $variables
 *   Theme variables to preprocess.
 */
function bw_mailing_preprocess_mimemail_message(array &$variables) {
  $variables['images_url'] = _bw_mailing_get_images_url();
}

/**
 * Implements hook_mail().
 */
function bw_mailing_mail($key, &$message, $params) {
  switch ($key) {
    case 'email_verification_email':
      $message['subject'] = new TranslatableMarkup('Please verify your email address for BRAYN');
      $message['body'][] = new TranslatableMarkup('Dear @name,', ['@name' => $params['name']]);
      $message['body'][] = new TranslatableMarkup('Thank you for signing up for BRAYN! To complete the registration process and access all of our features, we need to verify your email address.');
      $message['body'][] = new TranslatableMarkup('Please enter the following 6-digit activation code to verify your email address and confirm your account:');
      $message['body'][] = $params['verify_code'];
      $message['body'][] = new TranslatableMarkup('Please note that this code will expire in 30 minutes.');
      $message['body'][] = new TranslatableMarkup("Once you've completed this step, you'll be able to access all of the features of BRAYN and start learning new skills!");
      $message['body'][] = new TranslatableMarkup('Best regards,');
      $message['body'][] = new TranslatableMarkup('The BRAYN Team');
      break;

    case 'email_account_confirmation':
      $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed; delsp=yes';
      $message['params']['is_alt_variant'] = TRUE;
      $message['params']['heading'] = new TranslatableMarkup('Your upgrade has just started, <span style="color: #D5F20E">@name!</span><br><span style="color: #D5F20E"> Welcome to BRAYN!</span>', ['@name' => $params['name']]);
      $message['subject'] = strip_tags($message['params']['heading']);
      $message['body'][] = _bw_mailing_build_email_body($key, ['name' => $params['name']]);
      $message['params']['brayn_logo'] = TRUE;
      $message['params']['after_body'] = _bw_mailing_build_email_body("{$key}_after_body", [
        'name' => $params['name'],
        'images_url' => _bw_mailing_get_images_url(),
      ]);
      $message['params']['discord_link'] = 'https://go.brayn.app/3Th9glI';
      $message['params']['instagram_link'] = 'https://go.brayn.app/3RGub0c';
      break;

    case 'email_inactivity':
      $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed; delsp=yes';
      $message['params']['heading'] = new TranslatableMarkup('<span style="color: #D5F20E">@name,</span> How\'s your <span style="color: #FF1296;">BRAYN experience?</span>', ['@name' => $params['name']]);
      $message['subject'] = new TranslatableMarkup('@name, We Miss Your Neural Spark!', ['@name' => $params['name']]);
      $message['params']['heading_image_change'] = TRUE;
      $message['body'][] = _bw_mailing_build_email_body($key, [
        'name' => $params['name'],
      ]);
      $message['params']['action_url'] = 'https://go.brayn.app/47RQOoj';
      $message['params']['action_label'] = new TranslatableMarkup('CHECK UPDATES');
      $message['params']['brayn_logo'] = TRUE;
      $message['params']['discord_link'] = 'https://go.brayn.app/3uXY4jQ';
      $message['params']['instagram_link'] = 'https://go.brayn.app/3ti1MUR';
      break;

    case 'email_project_featured':
      $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed; delsp=yes';
      $message['params']['heading'] = new TranslatableMarkup('<span style="color: #D5F20E">@name,</span> your neurons are <span style="color: #FF1296;">lighting up BRAYN!</span>', ['@name' => $params['name']]);
      $message['subject'] = strip_tags($message['params']['heading']);
      $message['body'][] = _bw_mailing_build_email_body($key, [
        'name' => $params['name'],
      ]);
      $message['params']['action_url'] = Url::fromRoute(
        'bw_mailing.brayn_mobile_redirect',
        ['r' => 'https://go.brayn.app/3tmTkDJ'],
      )->toString();
      $message['params']['action_label'] = new TranslatableMarkup('CHECK YOUR PROJECT');
      $message['params']['brayn_logo'] = TRUE;
      $message['params']['after_body'] = _bw_mailing_build_email_body("{$key}_after_body", [
        'name' => $params['name'],
      ]);
      $message['params']['discord_link'] = 'https://go.brayn.app/3RHCRDq';
      $message['params']['instagram_link'] = 'https://go.brayn.app/46W656e';
      break;

    case 'email_rate_app':
      $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed; delsp=yes';
      $message['params']['heading'] = new TranslatableMarkup('<span style="color: #D5F20E">@name,</span> How\'s Your BRAYN experience?', ['@name' => $params['name']]);
      $message['subject'] = strip_tags($message['params']['heading']);
      $message['body'][] = _bw_mailing_build_email_body($key, [
        'name' => $params['name'],
      ]);
      $message['params']['action_url'] = 'https://go.brayn.app/41iPF6H';
      $message['params']['action_label'] = new TranslatableMarkup('SHARE');
      $message['params']['brayn_logo'] = TRUE;
      $message['params']['after_body'] = _bw_mailing_build_email_body("{$key}_after_body", [
        'name' => $params['name'],
      ]);
      $message['params']['discord_link'] = 'https://go.brayn.app/46WCg5d';
      $message['params']['instagram_link'] = 'https://go.brayn.app/3Tn9wzG';
      break;

    case 'email_project_approved':
      $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed; delsp=yes';
      $message['params']['heading'] = new TranslatableMarkup('Great Job, <span style="color: #D5F20E">@name!</span> <span style="color: #FF1296;">Your project is now</span> live on BRAYN!', ['@name' => $params['name']]);
      $message['subject'] = strip_tags($message['params']['heading']);
      $message['body'][] = _bw_mailing_build_email_body($key, [
        'name' => $params['name'],
      ]);
      $message['params']['action_url'] = Url::fromRoute(
        'bw_mailing.brayn_mobile_redirect',
        ['r' => 'https://go.brayn.app/471GloO'],
      )->toString();
      $message['params']['action_label'] = new TranslatableMarkup('CHECK YOUR PROJECT');
      $message['params']['brayn_logo'] = TRUE;
      $message['params']['discord_link'] = 'https://go.brayn.app/3NmJEQD';
      $message['params']['instagram_link'] = 'https://go.brayn.app/475kQUo';
      break;

    // Case 'email_forgotten_password':
    //   $message['subject'] = new TranslatableMarkup('Reset your BRAYN password');
    //   $message['body'][] = new TranslatableMarkup('Dear @name,', ['@name' => $params['name']]);
    //   $message['body'][] = new TranslatableMarkup("We've received a request to reset your BRAYN password. If you did not make this request, please disregard this email.");
    //   $message['body'][] = new TranslatableMarkup('To reset your password, please enter the following 6-digit code:');
    //   $message['body'][] = $params['reset_password_code'];
    //   $message['body'][] = new TranslatableMarkup('Please note that this code will expire in 30 minutes.');
    //   $message['body'][] = new TranslatableMarkup("If you have any questions or need further assistance, please don't hesitate to reach out to us.");
    //   $message['body'][] = new TranslatableMarkup('Best regards,');
    //   $message['body'][] = new TranslatableMarkup('The BRAYN Team');
    //   break;.
    // Case 'email_reset_password_confirmation':
    //   $message['subject'] = new TranslatableMarkup('Your BRAYN password has been reset');
    //   $message['body'][] = new TranslatableMarkup('Dear @name,', ['@name' => $params['name']]);
    //   $message['body'][] = new TranslatableMarkup('This is just a quick note to let you know that your BRAYN password has been successfully reset.');
    //   $message['body'][] = new TranslatableMarkup("If you have any questions or need further assistance, please don't hesitate to reach out to us.");
    //   $message['body'][] = new TranslatableMarkup('Best regards,');
    //   $message['body'][] = new TranslatableMarkup('The BRAYN Team');
    //   break;.
  }
}

/**
 * Returns the images URL for the module.
 *
 * @return string
 *   The URL to the module's images directory.
 */
function _bw_mailing_get_images_url() {
  return \Drupal::request()->getSchemeAndHttpHost()
    . '/'
    . \Drupal::service('extension.list.module')->getPath('bw_mailing')
    . '/images/icons/';
}

/**
 * Returns an email body by rendering a Twig template with the given parameters.
 *
 * @param array $params
 *   (optional) An array of parameters to pass to the Twig template.
 *
 * @return string
 *   The rendered email body as a string.
 */
function _bw_mailing_build_email_body(string $key, array $params = []) {
  return twig_render_template(
      \Drupal::service('extension.list.module')->getPath('bw_mailing') . '/templates/mail/partials/' . $key . '.html.twig',
      $params
  );
}
