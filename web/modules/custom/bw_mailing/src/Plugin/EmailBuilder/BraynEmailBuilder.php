<?php

namespace Drupal\bw_mailing\Plugin\EmailBuilder;

use <PERSON><PERSON><PERSON>\symfony_mailer\EmailInterface;
use <PERSON><PERSON>al\symfony_mailer\Processor\EmailBuilderBase;

/**
 * Defines the Email Builder plug-in.
 *
 * @EmailBuilder(
 *   id = "bw_mailing",
 *   sub_types = {
 *     "email_verification_email" = @Translation("Email Verification"),
 *     "email_account_confirmation" = @Translation("Account Confirmation"),
 *     "email_inactivity" = @Translation("Inactivity Reminder"),
 *     "email_project_featured" = @Translation("Project Featured"),
 *     "email_rate_app" = @Translation("Rate App"),
 *     "email_project_approved" = @Translation("Project Approved"),
 *     "email_forgotten_password" = @Translation("Forgotten Password"),
 *     "email_reset_password_confirmation" = @Translation("Password Reset Confirmation")
 *   },
 *   common_adjusters = {},
 *   import = @Translation(""),
 * )
 */
class BraynEmailBuilder extends EmailBuilderBase {

  /**
   * Saves the parameters for a newly created email.
   *
   * @param \Drupal\symfony_mailer\EmailInterface $email
   *   The email to modify.
   * @param array $params
   *   The parameters to save.
   */
  public function createParams(EmailInterface $email, $params = []) {
    $email->setParams($params);
  }

  /**
   * {@inheritdoc}
   */
  public function build(EmailInterface $email) {
    $headers = $email->getParams();
    $email->addTextHeader('customer_io_transactional_template_tokens', json_encode($headers));
  }

}
