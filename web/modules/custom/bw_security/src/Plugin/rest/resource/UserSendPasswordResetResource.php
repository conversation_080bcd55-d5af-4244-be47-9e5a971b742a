<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Language\LanguageManagerInterface;
use Drupal\rest\ModifiedResourceResponse;
use Dr<PERSON>al\rest\Plugin\ResourceBase;
use <PERSON><PERSON>al\symfony_mailer\EmailFactoryInterface;
use <PERSON><PERSON><PERSON>\user\UserInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * Send password reset email.
 *
 * @RestResource(
 *   id = "bw_user_send_password_reset",
 *   label = @Translation("BW: User: Send password reset"),
 *   uri_paths = {
 *     "create" = "/api/{version}/user/password-reset-send",
 *   },
 * )
 */
final class UserSendPasswordResetResource extends ResourceBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The email factory service.
   *
   * @var \Drupal\symfony_mailer\EmailFactoryInterface
   */
  protected EmailFactoryInterface $emailFactory;

  /**
   * Language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  private LanguageManagerInterface $languageManager;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\symfony_mailer\EmailFactoryInterface $email_factory
   *   The mail manager.
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   The language manager.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    EmailFactoryInterface $email_factory,
    LanguageManagerInterface $language_manager,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->emailFactory = $email_factory;
    $this->languageManager = $language_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('email_factory'),
      $container->get('language_manager'),
    );
  }

  /**
   * Resend email verification email.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The request.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function post(Request $request): ModifiedResourceResponse {
    $response = json_decode($request->getContent(), TRUE);
    if (!isset($response['email']) || !is_string($response['email'])) {
      throw new BadRequestHttpException('The request data is invalid.');
    }

    if (!filter_var($response['email'], FILTER_VALIDATE_EMAIL)) {
      throw new BadRequestHttpException('The email address is invalid.');
    }

    $users = $this->entityTypeManager->getStorage('user')
      ->loadByProperties(['mail' => $response['email'], 'status' => 1]);
    $user = $users ? reset($users) : NULL;
    if (!$user) {
      throw new BadRequestHttpException('The user could not be found or is not active.');
    }

    /** @var \Drupal\profile\ProfileStorageInterface $profile_storage */
    $profile_storage = $this->entityTypeManager->getStorage('profile');
    $student_profile = $profile_storage->loadByUser($user, 'student');
    $additional_data = $student_profile->field_s_additional_data->isEmpty()
      ? []
      : unserialize($student_profile->field_s_additional_data->value, ['allowed_classes' => FALSE]);
    $additional_data['reset_password'] = [
      'code' => $reset_password_code = str_pad(rand(0, 999999), '0', STR_PAD_LEFT),
      'expire' => (new \DateTime())->getTimestamp() + (30 * 60),
    ];
    $student_profile->set('field_s_additional_data', serialize($additional_data));
    $student_profile->save();

    $this->sendResetPasswordEmail(
      $user,
      $reset_password_code,
    );

    return new ModifiedResourceResponse(TRUE);
  }

  /**
   * Send verification email.
   *
   * @param \Drupal\user\UserInterface $user
   *   The user object.
   * @param string $reset_password_code
   *   The reset password code.
   */
  private function sendResetPasswordEmail(UserInterface $user, string $reset_password_code): void {
    $email = $this->emailFactory->newTypedEmail(
      'bw_mailing',
      'email_forgotten_password',
      [
        'reset_password_code' => $reset_password_code,
        'name' => $user->getAccountName(),
      ]);
    $email->setTo($user->getEmail());

    $result = $email->send();
    if (!$result) {
      $message = $this->t('There was a problem sending your email notification to @email.', ['@email' => $user->getEmail()]);
      $this->logger->error($message);

      return;
    }

    $message = $this->t('An email notification has been sent to @email.', ['@email' => $user->getEmail()]);
    $this->logger->notice($message);
  }

}
