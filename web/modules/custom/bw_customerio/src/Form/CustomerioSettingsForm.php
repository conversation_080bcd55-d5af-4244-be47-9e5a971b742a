<?php

namespace Drupal\bw_customerio\Form;

use Drupal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;

/**
 * Configure Customer.io settings for this site.
 */
class CustomerioSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'customerio_settings';
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['customerio.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('customerio.settings');

    $form['transactional_app_api_key'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Transactional App API Key'),
      '#default_value' => $config->get('transactional_app_api_key'),
      '#description' => $this->t('Enter your Customer.io Transactional App API Key.'),
      '#required' => TRUE,
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->config('customerio.settings')
      ->set('transactional_app_api_key', $form_state->getValue('transactional_app_api_key'))
      ->save();

    parent::submitForm($form, $form_state);
  }

}
