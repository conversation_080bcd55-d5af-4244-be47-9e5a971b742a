<?php

namespace Drupal\bw_customerio\Transport;

use Symfony\Component\Mailer\Exception\UnsupportedSchemeException;
use Symfony\Component\Mailer\Transport\AbstractTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Component\Mailer\Transport\TransportInterface;

/**
 * Provides a Customer.io transactional email API transport factory.
 */
final class TransactionalApiTransportFactory extends AbstractTransportFactory {

  /**
   * {@inheritdoc}
   */
  public function create(Dsn $dsn): TransportInterface {
    if ('customerio' === $dsn->getScheme()) {
      return new TransactionalApiTransport($this->dispatcher, $this->logger);
    }

    throw new UnsupportedSchemeException($dsn, 'customerio', $this->getSupportedSchemes());
  }

  /**
   * {@inheritdoc}
   */
  protected function getSupportedSchemes(): array {
    return ['customerio'];
  }

}
