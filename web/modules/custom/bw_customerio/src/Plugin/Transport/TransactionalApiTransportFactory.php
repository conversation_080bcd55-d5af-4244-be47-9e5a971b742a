<?php

namespace Drupal\bw_customerio\Transport;

use <PERSON>upal\Core\Config\ConfigFactoryInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\Exception\UnsupportedSchemeException;
use Symfony\Component\Mailer\Transport\AbstractTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;
use Symfony\Component\Mailer\Transport\TransportInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Provides a Customer.io transactional email API transport factory.
 */
final class TransactionalApiTransportFactory extends AbstractTransportFactory {

  /**
   * The config factory service.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  private ConfigFactoryInterface $configFactory;

  /**
   * Constructs a TransactionalApiTransportFactory object.
   *
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   The config factory service.
   * @param \Symfony\Component\EventDispatcher\EventDispatcherInterface|null $dispatcher
   *   The event dispatcher service (optional).
   * @param \Symfony\Contracts\HttpClient\HttpClientInterface|null $client
   *   The HTTP client service (optional).
   * @param \Psr\Log\LoggerInterface|null $logger
   *   The logger service (optional).
   */
  public function __construct(ConfigFactoryInterface $config_factory, ?EventDispatcherInterface $dispatcher = NULL, ?HttpClientInterface $client = NULL, ?LoggerInterface $logger = NULL) {
    $this->configFactory = $config_factory;
    parent::__construct($dispatcher, $client, $logger);
  }

  /**
   * {@inheritdoc}
   */
  public function create(Dsn $dsn): TransportInterface {
    if ('customerio' === $dsn->getScheme()) {
      return new TransactionalApiTransport($this->configFactory, $this->client, $this->dispatcher, $this->logger);
    }

    throw new UnsupportedSchemeException($dsn, 'customerio', $this->getSupportedSchemes());
  }

  /**
   * {@inheritdoc}
   */
  protected function getSupportedSchemes(): array {
    return ['customerio'];
  }

}
