<?php

namespace Drupal\bw_customerio\Transport;

use Drupal\Core\Config\ConfigFactoryInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Transport\AbstractApiTransport;
use Symfony\Component\Mime\Email;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

/**
 * Defines the Customer.io API transport mechanism for transactional emails.
 *
 * This class extends the AbstractApiTransport to implement sending of emails
 * via the Customer.io Transactional API.
 */
class TransactionalApiTransport extends AbstractApiTransport {

  /**
   * Default host for the Customer.io API.
   */
  private const HOST = 'api-eu.customer.io';

  /**
   * The config factory service.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  private ConfigFactoryInterface $configFactory;

  /**
   * Constructs a TransactionalApiTransport object.
   *
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   The config factory service.
   * @param \Symfony\Contracts\HttpClient\HttpClientInterface|null $client
   *   The HTTP client service (optional).
   * @param \Symfony\Component\EventDispatcher\EventDispatcherInterface|null $dispatcher
   *   The event dispatcher service (optional).
   * @param \Psr\Log\LoggerInterface|null $logger
   *   The logger service (optional).
   */
  public function __construct(ConfigFactoryInterface $config_factory, ?HttpClientInterface $client = NULL, ?EventDispatcherInterface $dispatcher = NULL, ?LoggerInterface $logger = NULL) {
    $this->configFactory = $config_factory;
    parent::__construct($client, $dispatcher, $logger);
  }

  /**
   * {@inheritdoc}
   */
  public function __toString(): string {
    return 'customerio://api-eu.customer.io';
  }

  /**
   * {@inheritdoc}
   */
  protected function doSendApi(SentMessage $sentMessage, Email $email, Envelope $envelope): ResponseInterface {
    $response = NULL;
    try {
      // Retrieve the API key from the configuration.
      $config = $this->configFactory->get('customerio.settings');
      $app_api_key = $config->get('transactional_app_api_key');

      // If the API key is empty, throw an exception to halt the process.
      if (empty($app_api_key)) {
        throw new \Exception('Failed to get app_api_key');
      }

      $response = $this->client->request('POST', 'https://' . self::HOST . '/v1/send/email', [
        'json' => $this->getPayload($email, $envelope),
        'auth_bearer' => $app_api_key,
      ]);

      // Extract the status code from the response.
      $status_code = $response->getStatusCode();
      if ($status_code == 200) {
        \Drupal::logger('bw_customerio')->info('Email sent successfully.');
      }
      else {
        \Drupal::logger('bw_customerio')->error('Failed to send email. Status code: @status_code', [
          '@status_code' => $status_code,
        ]);
      }
    }
    catch (\Exception | TransportExceptionInterface $exception) {
      // Log the exception details.
      \Drupal::logger('bw_customerio')->error('Exception occurred: @message. Trace: @trace', [
        '@message' => $exception->getMessage(),
        '@trace' => $exception->getTraceAsString(),
      ]);
    }

    return $response;
  }

  /**
   * Constructs the payload for a transactional email.
   *
   * This method builds an array payload to be sent to the API, using the
   * provided Email and Envelope objects. It includes essential details such as
   * the transactional message ID, recipient identifiers, and message data.
   *
   * @param $email
   *   The Email object containing the recipient and other email information.
   * @param $envelope
   *   The Envelope object containing additional sending options. Currently unused but can be
   *   utilized for future enhancements.
   *
   * @return array
   *   The constructed payload array for the API request.
   */
  private function getPayload(Email $email, Envelope $envelope): array {
    // Retrieve the transactional message ID.
    $transactional_message_id = $this->getTransactionalMessageId($email);
    $recipient_address = $email->getTo()[0]->getAddress();

    // Check if the recipient address is valid.
    if (empty($recipient_address)) {
      throw new \InvalidArgumentException("Recipient address is missing in the email object.");
    }
    $payload = [
      'transactional_message_id' => $transactional_message_id,
      'identifiers' => [
        'email' => $recipient_address,
      ],
      'to' => $recipient_address,
    ];

    // Message placeholders(tokens).
    $message_data = $this->getTransactionalMessageData($email);
    if (!empty($message_data)) {
      $payload['message_data'] = $message_data;
    }
    else {
      \Drupal::logger('bw_customerio')->warning('Empty message data for the email. To: @to, Transactional Message Id: @message_id', [
        '@to' => $recipient_address,
        '@message_id' => $transactional_message_id,
      ]);
    }

    return $payload;
  }

  /**
   * Retrieves the transactional message ID from an email's headers.
   *
   * This function extracts the Customer.io transactional template ID from the
   * headers of the provided Email object. It's used to identify the specific
   * transactional message template to be used in sending emails.
   *
   * @param \Symfony\Component\Mime\Email $email
   *   The Email object from which to extract the transactional message ID.
   *
   * @return int
   *   The extracted transactional message ID, or 0 if not found.
   */
  private function getTransactionalMessageId(Email $email): int {
    $transactional_message_id = NULL;
    $header = $email->getHeaders()->get('customer_io_transactional_template_id');
    if (!empty($header)) {
      $transactional_message_id = trim($email->getHeaders()->get('customer_io_transactional_template_id')->getBodyAsString());
    }

    return (int) $transactional_message_id;
  }

  /**
   * Extracts and decodes the transactional message data from an email's headers.
   *
   * This function is designed to retrieve Customer.io template tokens from the
   * headers of the provided Email object. These tokens are used to personalize
   * the content of the transactional email.
   *
   * @param \Symfony\Component\Mime\Email $email
   *   The Email object from which to extract the template tokens.
   *
   * @return array|null
   *   The decoded array of template tokens, or NULL if they are not found or on error.
   */
  private function getTransactionalMessageData(Email $email): ?array {
    try {
      $header = $email->getHeaders()->get('customer_io_transactional_template_tokens');
      if (!empty($header)) {
        return json_decode($header->getBodyAsString(), TRUE);
      }
    }
    catch (\Exception $exception) {
      \Drupal::logger('bw_customerio')->error('Failed to get template tokens from email.');
      return NULL;
    }

    return NULL;
  }

}
