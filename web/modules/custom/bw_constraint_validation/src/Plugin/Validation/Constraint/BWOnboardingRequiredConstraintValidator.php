<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Validates the BWOnboardingRequiredConstraint constraint.
 */
final class BWOnboardingRequiredConstraintValidator extends ConstraintValidator {

  /**
   * {@inheritdoc}
   */
  public function validate($entity, Constraint $constraint) {
    if (!$entity->isPublished() || $entity->bundle() !== 'onboarding') {
      return;
    }

    // Check direct reward fields.
    $has_direct_rewards = $this->hasDirectRewards($entity);

    // Check component rewards using form values.
    $has_component_rewards = $this->hasComponentRewards($entity);

    if (!$has_direct_rewards && !$has_component_rewards) {
      $this->context
        ->buildViolation($constraint->rewardMessage)
        ->addViolation();
    }
  }

  /**
   * Check if entity has direct reward values.
   *
   * @param mixed $entity
   *   The entity to check.
   *
   * @return bool
   *   TRUE if has direct rewards, FALSE otherwise.
   */
  private function hasDirectRewards($entity): bool {
    return (!$entity->get('field_rewards_axon')->isEmpty() && $entity->get('field_rewards_axon')->value)
      || (!$entity->get('field_rewards_neurons')->isEmpty() && $entity->get('field_rewards_neurons')->value);
  }

  /**
   * Check if entity has component rewards using form values.
   *
   * @param mixed $entity
   *   The entity to check.
   *
   * @return bool
   *   TRUE if has component rewards, FALSE otherwise.
   */
  private function hasComponentRewards($entity): bool {
    $field_components = $entity->get('field_components');

    if ($field_components->isEmpty()) {
      return FALSE;
    }

    foreach ($field_components as $component_item) {
      if ($this->checkComponentItemForRewards($component_item)) {
        return TRUE;
      }
    }

    return FALSE;
  }

  /**
   * Check if a component item has reward values.
   *
   * This method implements multiple approaches to access form values
   * based on your discovery of the entity structure.
   *
   * @param mixed $component_item
   *   The component field item.
   *
   * @return bool
   *   TRUE if component has reward values, FALSE otherwise.
   */
  private function checkComponentItemForRewards($component_item): bool {
    $component = $component_item->entity;

    if (!$component || $component->bundle() !== 'reward') {
      return FALSE;
    }

    if ($this->checkDirectRewardFields($component)) {
      return TRUE;
    }

    if ($this->checkSkillRewards($component)) {
      return TRUE;
    }

    return FALSE;
  }

  /**
   * Check direct reward fields on component.
   *
   * @param mixed $component
   *   The component entity.
   *
   * @return bool
   *   TRUE if has direct rewards, FALSE otherwise.
   */
  private function checkDirectRewardFields($component): bool {
    // Check neurons field.
    if (!$component->get('field_neurons')->isEmpty() && $component->get('field_neurons')->value) {
      return TRUE;
    }

    // Check axons field.
    if (!$component->get('field_axons')->isEmpty() && $component->get('field_axons')->value) {
      return TRUE;
    }

    // Check trophy field.
    if (!$component->get('field_trophy')->isEmpty()) {
      return TRUE;
    }

    return FALSE;
  }

  /**
   * Check skill rewards for braynbits.
   *
   * @param mixed $component
   *   The component entity.
   *
   * @return bool
   *   TRUE if has skill rewards, FALSE otherwise.
   */
  private function checkSkillRewards($component): bool {
    $field_skills = $component->get('field_skills');

    if ($field_skills->isEmpty()) {
      return FALSE;
    }

    // Iterate through skills to check for braynbits.
    foreach ($field_skills as $skill_item) {
      $skill = $skill_item->entity;
      if ($skill && !$skill->get('field_sc_braynbits')->isEmpty() && $skill->get('field_sc_braynbits')->value) {
        return TRUE;
      }
    }

    return FALSE;
  }

}
