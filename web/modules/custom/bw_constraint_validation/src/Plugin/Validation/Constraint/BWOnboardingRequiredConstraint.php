<?php

namespace Drupal\bw_constraint_validation\Plugin\Validation\Constraint;

use Symfony\Component\Validator\Constraint;

/**
 * Constrain for validation of onboarding required fields.
 *
 * @Constraint(
 *   id = "BWOnboardingRequiredConstraint",
 *   label = @Translation("BW onboarding required constraint", context = "Validation"),
 *   type = "string"
 * )
 */
final class BWOnboardingRequiredConstraint extends Constraint {

  /**
   * Constraint message for required.
   */
  public string $message = 'cannot be empty in order to publish.';

  /**
   * Constraint reward message for required.
   */
  public string $rewardMessage = 'At least one reward must be added to the onboarding in order to publish.';

}
