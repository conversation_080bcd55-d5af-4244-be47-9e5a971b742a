uuid: befb6fe7-5717-4784-b764-99e408a38de6
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_i_preview_image
    - paragraphs.paragraphs_type.infographic
  module:
    - image
id: paragraph.infographic.field_i_preview_image
field_name: field_i_preview_image
entity_type: paragraph
bundle: infographic
label: 'Preview image'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: '500 KB'
  max_resolution: ''
  min_resolution: ''
  alt_field: false
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
