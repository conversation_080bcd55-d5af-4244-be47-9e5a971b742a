uuid: 08d2ca48-f3a4-4a97-8a53-699ff537434c
langcode: en
status: true
dependencies:
  config:
    - field.field.node.course_structure_3.field_app_purchase_key
    - field.field.node.course_structure_3.field_app_purchase_key_dicount
    - field.field.node.course_structure_3.field_app_version
    - field.field.node.course_structure_3.field_badge
    - field.field.node.course_structure_3.field_cs1_comments_count
    - field.field.node.course_structure_3.field_cs1_description
    - field.field.node.course_structure_3.field_cs1_game_1
    - field.field.node.course_structure_3.field_cs1_game_10
    - field.field.node.course_structure_3.field_cs1_game_11
    - field.field.node.course_structure_3.field_cs1_game_12
    - field.field.node.course_structure_3.field_cs1_game_13
    - field.field.node.course_structure_3.field_cs1_game_14
    - field.field.node.course_structure_3.field_cs1_game_15
    - field.field.node.course_structure_3.field_cs1_game_16
    - field.field.node.course_structure_3.field_cs1_game_2
    - field.field.node.course_structure_3.field_cs1_game_3
    - field.field.node.course_structure_3.field_cs1_game_4
    - field.field.node.course_structure_3.field_cs1_game_5
    - field.field.node.course_structure_3.field_cs1_game_6
    - field.field.node.course_structure_3.field_cs1_game_7
    - field.field.node.course_structure_3.field_cs1_game_8
    - field.field.node.course_structure_3.field_cs1_game_9
    - field.field.node.course_structure_3.field_cs1_hashtags
    - field.field.node.course_structure_3.field_cs1_infographic_1
    - field.field.node.course_structure_3.field_cs1_infographic_10
    - field.field.node.course_structure_3.field_cs1_infographic_11
    - field.field.node.course_structure_3.field_cs1_infographic_12
    - field.field.node.course_structure_3.field_cs1_infographic_2
    - field.field.node.course_structure_3.field_cs1_infographic_3
    - field.field.node.course_structure_3.field_cs1_infographic_4
    - field.field.node.course_structure_3.field_cs1_infographic_5
    - field.field.node.course_structure_3.field_cs1_infographic_6
    - field.field.node.course_structure_3.field_cs1_infographic_7
    - field.field.node.course_structure_3.field_cs1_infographic_8
    - field.field.node.course_structure_3.field_cs1_infographic_9
    - field.field.node.course_structure_3.field_cs1_initial_favourites
    - field.field.node.course_structure_3.field_cs1_initial_likes
    - field.field.node.course_structure_3.field_cs1_initial_shares
    - field.field.node.course_structure_3.field_cs1_initial_views
    - field.field.node.course_structure_3.field_cs1_intro_video
    - field.field.node.course_structure_3.field_cs1_less_10_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_11_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_12_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_13_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_14_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_15_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_16_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_17_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_18_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_19_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_1_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_20_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_21_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_22_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_23_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_24_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_25_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_26_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_27_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_28_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_2_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_3_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_4_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_5_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_6_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_7_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_8_initial_likes
    - field.field.node.course_structure_3.field_cs1_less_9_initial_likes
    - field.field.node.course_structure_3.field_cs1_lesson_1
    - field.field.node.course_structure_3.field_cs1_lesson_10
    - field.field.node.course_structure_3.field_cs1_lesson_11
    - field.field.node.course_structure_3.field_cs1_lesson_12
    - field.field.node.course_structure_3.field_cs1_lesson_13
    - field.field.node.course_structure_3.field_cs1_lesson_14
    - field.field.node.course_structure_3.field_cs1_lesson_15
    - field.field.node.course_structure_3.field_cs1_lesson_16
    - field.field.node.course_structure_3.field_cs1_lesson_17
    - field.field.node.course_structure_3.field_cs1_lesson_18
    - field.field.node.course_structure_3.field_cs1_lesson_19
    - field.field.node.course_structure_3.field_cs1_lesson_2
    - field.field.node.course_structure_3.field_cs1_lesson_20
    - field.field.node.course_structure_3.field_cs1_lesson_21
    - field.field.node.course_structure_3.field_cs1_lesson_22
    - field.field.node.course_structure_3.field_cs1_lesson_23
    - field.field.node.course_structure_3.field_cs1_lesson_24
    - field.field.node.course_structure_3.field_cs1_lesson_25
    - field.field.node.course_structure_3.field_cs1_lesson_26
    - field.field.node.course_structure_3.field_cs1_lesson_27
    - field.field.node.course_structure_3.field_cs1_lesson_28
    - field.field.node.course_structure_3.field_cs1_lesson_3
    - field.field.node.course_structure_3.field_cs1_lesson_4
    - field.field.node.course_structure_3.field_cs1_lesson_5
    - field.field.node.course_structure_3.field_cs1_lesson_6
    - field.field.node.course_structure_3.field_cs1_lesson_7
    - field.field.node.course_structure_3.field_cs1_lesson_8
    - field.field.node.course_structure_3.field_cs1_lesson_9
    - field.field.node.course_structure_3.field_cs1_likes_count
    - field.field.node.course_structure_3.field_cs1_overview_video
    - field.field.node.course_structure_3.field_cs1_payment_type
    - field.field.node.course_structure_3.field_cs1_preview_video
    - field.field.node.course_structure_3.field_cs1_project_task_descripti
    - field.field.node.course_structure_3.field_cs1_project_task_max_video
    - field.field.node.course_structure_3.field_cs1_project_task_preview
    - field.field.node.course_structure_3.field_cs1_project_task_preview_r
    - field.field.node.course_structure_3.field_cs1_project_task_title
    - field.field.node.course_structure_3.field_cs1_skills
    - field.field.node.course_structure_3.field_in_app_purchase_key_so
    - image.style.thumbnail
    - node.type.course_structure_3
  module:
    - field_group
    - image
    - maxlength
    - paragraphs
third_party_settings:
  field_group:
    group_initial_stats:
      children:
        - field_cs1_initial_likes
        - field_cs1_initial_favourites
        - field_cs1_initial_shares
        - field_cs1_initial_views
      label: 'Initial stats'
      region: content
      parent_name: ''
      weight: 8
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_access:
      children:
        - field_cs1_payment_type
      label: Access
      region: content
      parent_name: ''
      weight: 9
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
    group_sections:
      children:
        - group_preview
        - group_section_1
        - group_section_2
        - group_section_3
        - group_section_4
        - group_section_5
        - group_section_6
        - group_section_7
        - group_section_8
        - group_section_9
        - group_section_10
        - group_section_11
        - group_section_12
        - group_section_13
        - group_section_14
        - group_section_15
        - group_section_16
        - group_section_17
        - group_section_18
        - group_section_19
        - group_section_20
        - group_section_21
        - group_section_22
        - group_section_23
        - group_section_24
        - group_section_25
        - group_section_26
        - group_section_27
        - group_section_28
        - group_section_29
        - group_section_30
        - group_section_31
        - group_section_32
        - group_section_33
        - group_section_34
        - group_section_35
        - group_section_36
        - group_section_37
        - group_section_38
        - group_section_39
        - group_section_40
        - group_section_41
        - group_section_42
        - group_section_43
        - group_section_44
        - group_section_45
        - group_section_46
      label: Sections
      region: content
      parent_name: ''
      weight: 10
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: vertical
        width_breakpoint: 640
    group_preview:
      children:
        - field_cs1_preview_video
      label: Preview
      region: content
      parent_name: group_sections
      weight: 20
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_1:
      children:
        - field_cs1_intro_video
        - field_cs1_game_1
        - field_cs1_infographic_1
      label: 'Section #1'
      region: content
      parent_name: group_sections
      weight: 21
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_2:
      children:
        - field_cs1_game_2
        - field_cs1_infographic_2
      label: 'Section #2'
      region: content
      parent_name: group_sections
      weight: 22
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_3:
      children:
        - field_cs1_game_3
      label: 'Section #3'
      region: content
      parent_name: group_sections
      weight: 23
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_4:
      children:
        - field_cs1_lesson_1
        - field_cs1_less_1_initial_likes
      label: 'Section #4'
      region: content
      parent_name: group_sections
      weight: 24
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_5:
      children:
        - field_cs1_lesson_2
        - field_cs1_less_2_initial_likes
      label: 'Section #5'
      region: content
      parent_name: group_sections
      weight: 25
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_6:
      children:
        - field_cs1_lesson_3
        - field_cs1_less_3_initial_likes
      label: 'Section #6'
      region: content
      parent_name: group_sections
      weight: 26
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_7:
      children:
        - field_cs1_lesson_4
        - field_cs1_less_4_initial_likes
      label: 'Section #7'
      region: content
      parent_name: group_sections
      weight: 27
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_8:
      children:
        - field_cs1_game_4
        - field_cs1_infographic_3
      label: 'Section #8'
      region: content
      parent_name: group_sections
      weight: 29
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_9:
      children:
        - field_cs1_game_5
      label: 'Section #9'
      region: content
      parent_name: group_sections
      weight: 30
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_10:
      children:
        - field_cs1_lesson_5
        - field_cs1_less_5_initial_likes
      label: 'Section #10'
      region: content
      parent_name: group_sections
      weight: 31
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_11:
      children:
        - field_cs1_lesson_6
        - field_cs1_less_6_initial_likes
      label: 'Section #11'
      region: content
      parent_name: group_sections
      weight: 32
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_12:
      children:
        - field_cs1_lesson_7
        - field_cs1_less_7_initial_likes
      label: 'Section #12'
      region: content
      parent_name: group_sections
      weight: 33
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_13:
      children:
        - field_cs1_lesson_8
        - field_cs1_less_8_initial_likes
      label: 'Section #13'
      region: content
      parent_name: group_sections
      weight: 34
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_14:
      children:
        - field_cs1_game_6
        - field_cs1_infographic_4
      label: 'Section #14'
      region: content
      parent_name: group_sections
      weight: 35
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_15:
      children:
        - field_cs1_game_7
      label: 'Section #15'
      region: content
      parent_name: group_sections
      weight: 36
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_16:
      children:
        - field_cs1_lesson_9
        - field_cs1_less_9_initial_likes
      label: 'Section #16'
      region: content
      parent_name: group_sections
      weight: 37
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_17:
      children:
        - field_cs1_lesson_10
        - field_cs1_less_10_initial_likes
      label: 'Section #17'
      region: content
      parent_name: group_sections
      weight: 38
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_18:
      children:
        - field_cs1_lesson_11
        - field_cs1_less_11_initial_likes
      label: 'Section #18'
      region: content
      parent_name: group_sections
      weight: 39
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_19:
      children:
        - field_cs1_lesson_12
        - field_cs1_less_12_initial_likes
      label: 'Section #19'
      region: content
      parent_name: group_sections
      weight: 40
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_20:
      children:
        - field_cs1_game_8
        - field_cs1_infographic_5
      label: 'Section #20'
      region: content
      parent_name: group_sections
      weight: 41
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_21:
      children:
        - field_cs1_game_9
      label: 'Section #21'
      region: content
      parent_name: group_sections
      weight: 42
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_22:
      children:
        - field_cs1_lesson_13
        - field_cs1_less_13_initial_likes
      label: 'Section #22'
      region: content
      parent_name: group_sections
      weight: 43
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_23:
      children:
        - field_cs1_lesson_14
        - field_cs1_less_14_initial_likes
      label: 'Section #23'
      region: content
      parent_name: group_sections
      weight: 44
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_24:
      children:
        - field_cs1_lesson_15
        - field_cs1_less_15_initial_likes
      label: 'Section #24'
      region: content
      parent_name: group_sections
      weight: 45
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_25:
      children:
        - field_cs1_lesson_16
        - field_cs1_less_16_initial_likes
      label: 'Section #25'
      region: content
      parent_name: group_sections
      weight: 46
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_26:
      children:
        - field_cs1_game_10
        - field_cs1_infographic_6
      label: 'Section #26'
      region: content
      parent_name: group_sections
      weight: 47
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_27:
      children:
        - field_cs1_game_11
      label: 'Section #27'
      region: content
      parent_name: group_sections
      weight: 48
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_28:
      children:
        - field_cs1_lesson_17
        - field_cs1_less_17_initial_likes
      label: 'Section #28'
      region: content
      parent_name: group_sections
      weight: 49
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_29:
      children:
        - field_cs1_lesson_18
        - field_cs1_less_18_initial_likes
      label: 'Section #29'
      region: content
      parent_name: group_sections
      weight: 50
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_30:
      children:
        - field_cs1_lesson_19
        - field_cs1_less_19_initial_likes
      label: 'Section #30'
      region: content
      parent_name: group_sections
      weight: 51
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_31:
      children:
        - field_cs1_lesson_20
        - field_cs1_less_20_initial_likes
      label: 'Section #31'
      region: content
      parent_name: group_sections
      weight: 52
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_32:
      children:
        - field_cs1_game_12
        - field_cs1_infographic_7
      label: 'Section #32'
      region: content
      parent_name: group_sections
      weight: 53
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_33:
      children:
        - field_cs1_game_13
      label: 'Section #33'
      region: content
      parent_name: group_sections
      weight: 54
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_34:
      children:
        - field_cs1_lesson_21
        - field_cs1_less_21_initial_likes
      label: 'Section #34'
      region: content
      parent_name: group_sections
      weight: 55
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_35:
      children:
        - field_cs1_lesson_22
        - field_cs1_less_22_initial_likes
      label: 'Section #35'
      region: content
      parent_name: group_sections
      weight: 56
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_36:
      children:
        - field_cs1_lesson_23
        - field_cs1_less_23_initial_likes
      label: 'Section #36'
      region: content
      parent_name: group_sections
      weight: 57
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_37:
      children:
        - field_cs1_lesson_24
        - field_cs1_less_24_initial_likes
      label: 'Section #37'
      region: content
      parent_name: group_sections
      weight: 58
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_38:
      children:
        - field_cs1_game_14
        - field_cs1_infographic_8
      label: 'Section #38'
      region: content
      parent_name: group_sections
      weight: 59
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_39:
      children:
        - field_cs1_game_15
      label: 'Section #39'
      region: content
      parent_name: group_sections
      weight: 60
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_40:
      children:
        - field_cs1_lesson_25
        - field_cs1_less_25_initial_likes
      label: 'Section #40'
      region: content
      parent_name: group_sections
      weight: 61
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_41:
      children:
        - field_cs1_lesson_26
        - field_cs1_less_26_initial_likes
      label: 'Section #41'
      region: content
      parent_name: group_sections
      weight: 62
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_42:
      children:
        - field_cs1_lesson_27
        - field_cs1_less_27_initial_likes
      label: 'Section #42'
      region: content
      parent_name: group_sections
      weight: 63
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_43:
      children:
        - field_cs1_lesson_28
        - field_cs1_less_28_initial_likes
      label: 'Section #43'
      region: content
      parent_name: group_sections
      weight: 64
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_44:
      children:
        - field_cs1_game_16
        - field_cs1_infographic_9
      label: 'Section #44'
      region: content
      parent_name: group_sections
      weight: 65
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_45:
      children:
        - group_course_project_task
        - field_cs1_infographic_10
        - field_cs1_infographic_11
        - field_cs1_infographic_12
      label: 'Section #45'
      region: content
      parent_name: group_sections
      weight: 66
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_46:
      children:
        - field_cs1_overview_video
      label: 'Section #46'
      region: content
      parent_name: group_sections
      weight: 67
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_course_project_task:
      children:
        - field_cs1_project_task_title
        - field_cs1_project_task_descripti
        - field_cs1_project_task_max_video
        - field_cs1_project_task_preview
        - field_cs1_project_task_preview_r
      label: 'Course project task'
      region: content
      parent_name: group_section_45
      weight: 20
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        description: ''
        required_fields: true
id: node.course_structure_3.default
targetEntityType: node
bundle: course_structure_3
mode: default
content:
  field_app_purchase_key:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_app_purchase_key_dicount:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_app_version:
    type: number
    weight: 4
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_badge:
    type: image_image
    weight: 8
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_cs1_description:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 100
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 100
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_cs1_game_1:
    type: paragraphs
    weight: 41
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_10:
    type: paragraphs
    weight: 19
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_11:
    type: paragraphs
    weight: 20
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_cs1_game_12:
    type: paragraphs
    weight: 21
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_13:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_14:
    type: paragraphs
    weight: 23
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_15:
    type: paragraphs
    weight: 24
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_16:
    type: paragraphs
    weight: 25
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_2:
    type: paragraphs
    weight: 11
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_3:
    type: paragraphs
    weight: 12
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_4:
    type: paragraphs
    weight: 13
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_5:
    type: paragraphs
    weight: 14
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_6:
    type: paragraphs
    weight: 15
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_7:
    type: paragraphs
    weight: 16
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_8:
    type: paragraphs
    weight: 41
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_9:
    type: paragraphs
    weight: 18
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_hashtags:
    type: entity_reference_autocomplete_tags
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cs1_infographic_1:
    type: paragraphs
    weight: 42
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_10:
    type: paragraphs
    weight: 21
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_11:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_12:
    type: paragraphs
    weight: 23
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_2:
    type: paragraphs
    weight: 12
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_3:
    type: paragraphs
    weight: 14
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_4:
    type: paragraphs
    weight: 16
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_5:
    type: paragraphs
    weight: 42
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_6:
    type: paragraphs
    weight: 20
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_7:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_8:
    type: paragraphs
    weight: 24
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_9:
    type: paragraphs
    weight: 26
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_initial_favourites:
    type: number
    weight: 39
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_initial_likes:
    type: number
    weight: 38
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_initial_shares:
    type: number
    weight: 40
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_initial_views:
    type: number
    weight: 41
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_intro_video:
    type: paragraphs
    weight: 40
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_less_10_initial_likes:
    type: number
    weight: 55
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_11_initial_likes:
    type: number
    weight: 56
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_12_initial_likes:
    type: number
    weight: 57
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_13_initial_likes:
    type: number
    weight: 58
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_14_initial_likes:
    type: number
    weight: 59
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_15_initial_likes:
    type: number
    weight: 60
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_16_initial_likes:
    type: number
    weight: 61
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_17_initial_likes:
    type: number
    weight: 62
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_18_initial_likes:
    type: number
    weight: 63
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_19_initial_likes:
    type: number
    weight: 64
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_1_initial_likes:
    type: number
    weight: 46
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_20_initial_likes:
    type: number
    weight: 65
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_21_initial_likes:
    type: number
    weight: 66
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_22_initial_likes:
    type: number
    weight: 67
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_23_initial_likes:
    type: number
    weight: 68
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_24_initial_likes:
    type: number
    weight: 69
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_25_initial_likes:
    type: number
    weight: 70
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_26_initial_likes:
    type: number
    weight: 71
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_27_initial_likes:
    type: number
    weight: 72
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_28_initial_likes:
    type: number
    weight: 73
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_2_initial_likes:
    type: number
    weight: 47
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_3_initial_likes:
    type: number
    weight: 48
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_4_initial_likes:
    type: number
    weight: 49
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_5_initial_likes:
    type: number
    weight: 50
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_6_initial_likes:
    type: number
    weight: 51
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_7_initial_likes:
    type: number
    weight: 52
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_8_initial_likes:
    type: number
    weight: 53
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_9_initial_likes:
    type: number
    weight: 18
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_lesson_1:
    type: paragraphs
    weight: 45
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_10:
    type: paragraphs
    weight: 54
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_11:
    type: paragraphs
    weight: 55
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_12:
    type: paragraphs
    weight: 56
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_13:
    type: paragraphs
    weight: 57
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_14:
    type: paragraphs
    weight: 58
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_15:
    type: paragraphs
    weight: 59
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_16:
    type: paragraphs
    weight: 60
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_17:
    type: paragraphs
    weight: 61
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_18:
    type: paragraphs
    weight: 62
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_19:
    type: paragraphs
    weight: 63
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_2:
    type: paragraphs
    weight: 46
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_20:
    type: paragraphs
    weight: 64
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_21:
    type: paragraphs
    weight: 65
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_22:
    type: paragraphs
    weight: 66
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_23:
    type: paragraphs
    weight: 67
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_24:
    type: paragraphs
    weight: 68
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_25:
    type: paragraphs
    weight: 69
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_26:
    type: paragraphs
    weight: 70
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_27:
    type: paragraphs
    weight: 71
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_28:
    type: paragraphs
    weight: 72
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_3:
    type: paragraphs
    weight: 47
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_4:
    type: paragraphs
    weight: 48
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_5:
    type: paragraphs
    weight: 49
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_6:
    type: paragraphs
    weight: 50
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_7:
    type: paragraphs
    weight: 51
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_8:
    type: paragraphs
    weight: 52
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_9:
    type: paragraphs
    weight: 17
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_overview_video:
    type: paragraphs
    weight: 42
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_payment_type:
    type: paragraphs
    weight: 74
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_preview_video:
    type: paragraphs
    weight: 45
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_project_task_descripti:
    type: string_textfield
    weight: 45
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 255
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_cs1_project_task_max_video:
    type: options_select
    weight: 46
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cs1_project_task_preview:
    type: image_image
    weight: 47
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_cs1_project_task_preview_r:
    type: image_image
    weight: 48
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_cs1_project_task_title:
    type: string_textfield
    weight: 44
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 60
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_cs1_skills:
    type: paragraphs
    weight: 3
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: skill_course
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_in_app_purchase_key_so:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 11
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 35
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 35
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
hidden:
  created: true
  field_cs1_comments_count: true
  field_cs1_likes_count: true
  langcode: true
  promote: true
  sticky: true
  uid: true
