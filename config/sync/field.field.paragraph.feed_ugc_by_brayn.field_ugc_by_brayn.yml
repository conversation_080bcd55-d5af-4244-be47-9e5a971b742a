uuid: f8427a84-5bb8-43ca-bc1a-0216bb71647b
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_ugc_by_brayn
    - node.type.ugc_by_brayn
    - paragraphs.paragraphs_type.feed_ugc_by_brayn
id: paragraph.feed_ugc_by_brayn.field_ugc_by_brayn
field_name: field_ugc_by_brayn
entity_type: paragraph
bundle: feed_ugc_by_brayn
label: 'UGC by Brayn'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      ugc_by_brayn: ugc_by_brayn
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
