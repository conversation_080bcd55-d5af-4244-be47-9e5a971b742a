uuid: bbe86c3d-a384-409d-9f0c-5586462b42c0
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_vt_customised_ugc_pending_
    - media.type.image
    - paragraphs.paragraphs_type.video_task
id: paragraph.video_task.field_vt_customised_ugc_pending_
field_name: field_vt_customised_ugc_pending_
entity_type: paragraph
bundle: video_task
label: 'Customised UGC pending image'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
