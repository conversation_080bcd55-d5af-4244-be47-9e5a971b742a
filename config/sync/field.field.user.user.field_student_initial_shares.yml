uuid: 19d5cf7c-821c-4fc4-af8f-4e24c2d9986b
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.field_student_initial_shares
  module:
    - user
id: user.user.field_student_initial_shares
field_name: field_student_initial_shares
entity_type: user
bundle: user
label: 'Student initial shares'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: 0
  max: null
  prefix: ''
  suffix: ''
field_type: integer
