uuid: a97ab5e0-ac71-4be2-a3f4-5f64645136d7
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_ws_date_start
    - paragraphs.paragraphs_type.work_student
  module:
    - datetime
id: paragraph.work_student.field_ws_date_start
field_name: field_ws_date_start
entity_type: paragraph
bundle: work_student
label: 'Start date'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
