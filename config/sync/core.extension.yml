_core:
  default_config_hash: R4IF-ClDHXxblLcG0L7MgsLvfBIMAvi_skumNFQwkDc
module:
  admin_toolbar: 0
  admin_toolbar_links_access_filter: 0
  administerusersbyrole: 0
  autosave_form: 0
  basic_auth: 0
  block: 0
  breakpoint: 0
  bw_comments: 0
  bw_constraint_validation: 0
  bw_customerio: 0
  bw_flagging: 0
  bw_mailing: 0
  bw_market: 0
  bw_misc: 0
  bw_nanoboost_progress: 0
  bw_notifications: 0
  bw_onesignal: 0
  bw_requests: 0
  bw_rest_resources: 0
  bw_rewards: 0
  bw_search: 0
  bw_security: 0
  bw_smart_goal: 0
  bw_token: 0
  bw_trophies: 0
  bw_user_activity: 0
  bw_user_branch_progress: 0
  bw_user_course_progress: 0
  bw_user_shortzzz_project: 0
  bw_vimeo: 0
  ckeditor5: 0
  computed_field: 0
  computed_field_ui: 0
  config: 0
  config_filter: 0
  config_ignore: 0
  config_pages: 0
  config_split: 0
  customerio: 0
  datetime: 0
  dynamic_page_cache: 0
  editor: 0
  entity: 0
  entity_reference_revisions: 0
  entity_share: 0
  entity_share_async: 0
  entity_share_client: 0
  entity_share_diff: 0
  entity_share_server: 0
  field: 0
  field_group: 0
  field_ui: 0
  file: 0
  filter: 0
  gin_toolbar: 0
  image: 0
  jsonapi: 0
  language: 0
  link: 0
  mailsystem: 0
  maxlength: 0
  media: 0
  media_bulk_upload: 0
  media_library: 0
  mimemail: 0
  mysql: 0
  node: 0
  options: 0
  pantheon_advanced_page_cache: 0
  paragraphs_type_permissions: 0
  path_alias: 0
  phpass: 0
  profile: 0
  raven: 0
  redis: 0
  rest: 0
  s3fs: 0
  scroll_top_button: 0
  search_api: 0
  search_api_pantheon: 0
  search_api_solr: 0
  sendgrid_integration: 0
  serialization: 0
  symfony_mailer: 0
  system: 0
  taxonomy: 0
  text: 0
  toolbar: 0
  ultimate_cron: 0
  update: 0
  user: 0
  video_embed_field: 0
  views_autocomplete_filters: 0
  views_bulk_edit: 0
  views_ui: 0
  wordfilter: 0
  views: 10
  paragraphs: 11
  minimal: 1000
theme:
  claro: 0
  gin: 0
profile: minimal
