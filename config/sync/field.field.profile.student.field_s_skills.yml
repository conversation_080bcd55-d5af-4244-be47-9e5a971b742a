uuid: f45d5789-a7cd-478c-8e7b-109ba4fb2b05
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_s_skills
    - paragraphs.paragraphs_type.skill_student
    - profile.type.student
  module:
    - entity_reference_revisions
id: profile.student.field_s_skills
field_name: field_s_skills
entity_type: profile
bundle: student
label: Skills
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      skill_student: skill_student
    negate: 0
    target_bundles_drag_drop:
      access_axons:
        weight: 15
        enabled: false
      access_neurons:
        weight: 16
        enabled: false
      infographic:
        weight: 19
        enabled: false
      quiz_image:
        weight: 20
        enabled: false
      quiz_image_question:
        weight: 21
        enabled: false
      quiz_tinder_mechanics:
        weight: 22
        enabled: false
      quiz_tinder_mechanics_question:
        weight: 23
        enabled: false
      skill_course:
        weight: 24
        enabled: false
      skill_student:
        weight: 14
        enabled: true
      video:
        weight: 26
        enabled: false
field_type: entity_reference_revisions
