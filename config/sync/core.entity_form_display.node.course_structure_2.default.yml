uuid: aa54ca3a-b136-4c04-a9e1-4024db93dda8
langcode: en
status: true
dependencies:
  config:
    - field.field.node.course_structure_2.field_app_purchase_key
    - field.field.node.course_structure_2.field_app_purchase_key_dicount
    - field.field.node.course_structure_2.field_app_version
    - field.field.node.course_structure_2.field_badge
    - field.field.node.course_structure_2.field_cs1_comments_count
    - field.field.node.course_structure_2.field_cs1_description
    - field.field.node.course_structure_2.field_cs1_game_1
    - field.field.node.course_structure_2.field_cs1_game_2
    - field.field.node.course_structure_2.field_cs1_game_3
    - field.field.node.course_structure_2.field_cs1_game_4
    - field.field.node.course_structure_2.field_cs1_hashtags
    - field.field.node.course_structure_2.field_cs1_infographic_1
    - field.field.node.course_structure_2.field_cs1_infographic_2
    - field.field.node.course_structure_2.field_cs1_infographic_3
    - field.field.node.course_structure_2.field_cs1_infographic_4
    - field.field.node.course_structure_2.field_cs1_infographic_5
    - field.field.node.course_structure_2.field_cs1_infographic_6
    - field.field.node.course_structure_2.field_cs1_initial_favourites
    - field.field.node.course_structure_2.field_cs1_initial_likes
    - field.field.node.course_structure_2.field_cs1_initial_shares
    - field.field.node.course_structure_2.field_cs1_initial_views
    - field.field.node.course_structure_2.field_cs1_intro_video
    - field.field.node.course_structure_2.field_cs1_less_1_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_2_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_3_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_4_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_5_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_6_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_7_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_8_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_9_initial_likes
    - field.field.node.course_structure_2.field_cs1_lesson_1
    - field.field.node.course_structure_2.field_cs1_lesson_2
    - field.field.node.course_structure_2.field_cs1_lesson_3
    - field.field.node.course_structure_2.field_cs1_lesson_4
    - field.field.node.course_structure_2.field_cs1_lesson_5
    - field.field.node.course_structure_2.field_cs1_lesson_6
    - field.field.node.course_structure_2.field_cs1_lesson_7
    - field.field.node.course_structure_2.field_cs1_lesson_8
    - field.field.node.course_structure_2.field_cs1_lesson_9
    - field.field.node.course_structure_2.field_cs1_likes_count
    - field.field.node.course_structure_2.field_cs1_overview_video
    - field.field.node.course_structure_2.field_cs1_payment_type
    - field.field.node.course_structure_2.field_cs1_preview_video
    - field.field.node.course_structure_2.field_cs1_project_task_descripti
    - field.field.node.course_structure_2.field_cs1_project_task_max_video
    - field.field.node.course_structure_2.field_cs1_project_task_preview
    - field.field.node.course_structure_2.field_cs1_project_task_preview_r
    - field.field.node.course_structure_2.field_cs1_project_task_title
    - field.field.node.course_structure_2.field_cs1_skills
    - field.field.node.course_structure_2.field_in_app_purchase_key_so
    - image.style.thumbnail
    - node.type.course_structure_2
  module:
    - field_group
    - image
    - maxlength
    - paragraphs
third_party_settings:
  field_group:
    group_initial_stats:
      children:
        - field_cs1_initial_likes
        - field_cs1_initial_favourites
        - field_cs1_initial_shares
        - field_cs1_initial_views
      label: 'Initial stats'
      region: content
      parent_name: ''
      weight: 8
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: true
    group_access:
      children:
        - field_cs1_payment_type
      label: Access
      region: content
      parent_name: ''
      weight: 9
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
    group_sections:
      children:
        - group_preview
        - group_section_1
        - group_section_2
        - group_section_3
        - group_section_4
        - group_section_5
        - group_section_6
        - group_section_7
        - group_section_8
        - group_section_9
        - group_section_10
        - group_section_11
        - group_section_12
        - group_section_13
        - group_section_14
        - group_section_15
      label: Sections
      region: content
      parent_name: ''
      weight: 10
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: vertical
        width_breakpoint: 640
    group_preview:
      children:
        - field_cs1_preview_video
      label: Preview
      region: content
      parent_name: group_sections
      weight: 37
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_1:
      children:
        - field_cs1_intro_video
        - field_cs1_game_1
        - field_cs1_infographic_1
      label: 'Section #1'
      region: content
      parent_name: group_sections
      weight: 38
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_2:
      children:
        - field_cs1_lesson_1
        - field_cs1_less_1_initial_likes
      label: 'Section #2'
      region: content
      parent_name: group_sections
      weight: 39
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_3:
      children:
        - field_cs1_lesson_2
        - field_cs1_less_2_initial_likes
      label: 'Section #3'
      region: content
      parent_name: group_sections
      weight: 41
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_4:
      children:
        - field_cs1_lesson_3
        - field_cs1_less_3_initial_likes
      label: 'Section #4'
      region: content
      parent_name: group_sections
      weight: 42
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_5:
      children:
        - field_cs1_game_2
        - field_cs1_infographic_2
      label: 'Section #5'
      region: content
      parent_name: group_sections
      weight: 43
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_6:
      children:
        - field_cs1_lesson_4
        - field_cs1_less_4_initial_likes
      label: 'Section #6'
      region: content
      parent_name: group_sections
      weight: 44
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_7:
      children:
        - field_cs1_lesson_5
        - field_cs1_less_5_initial_likes
      label: 'Section #7'
      region: content
      parent_name: group_sections
      weight: 45
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_8:
      children:
        - field_cs1_lesson_6
        - field_cs1_less_6_initial_likes
      label: 'Section #8'
      region: content
      parent_name: group_sections
      weight: 46
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_9:
      children:
        - field_cs1_game_3
        - field_cs1_infographic_3
      label: 'Section #9'
      region: content
      parent_name: group_sections
      weight: 47
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_10:
      children:
        - field_cs1_lesson_7
        - field_cs1_less_7_initial_likes
      label: 'Section #10'
      region: content
      parent_name: group_sections
      weight: 48
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_11:
      children:
        - field_cs1_lesson_8
        - field_cs1_less_8_initial_likes
      label: 'Section #11'
      region: content
      parent_name: group_sections
      weight: 49
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_12:
      children:
        - field_cs1_lesson_9
        - field_cs1_less_9_initial_likes
      label: 'Section #12'
      region: content
      parent_name: group_sections
      weight: 50
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_13:
      children:
        - field_cs1_game_4
      label: 'Section #13'
      region: content
      parent_name: group_sections
      weight: 51
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_14:
      children:
        - group_course_project_task
        - field_cs1_infographic_4
        - field_cs1_infographic_5
        - field_cs1_infographic_6
      label: 'Section #14'
      region: content
      parent_name: group_sections
      weight: 52
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_section_15:
      children:
        - field_cs1_overview_video
      label: 'Section #15'
      region: content
      parent_name: group_sections
      weight: 53
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_course_project_task:
      children:
        - field_cs1_project_task_title
        - field_cs1_project_task_descripti
        - field_cs1_project_task_max_video
        - field_cs1_project_task_preview
        - field_cs1_project_task_preview_r
      label: 'Course project task'
      region: content
      parent_name: group_section_14
      weight: 20
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
id: node.course_structure_2.default
targetEntityType: node
bundle: course_structure_2
mode: default
content:
  field_app_purchase_key:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_app_purchase_key_dicount:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_app_version:
    type: number
    weight: 4
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_badge:
    type: image_image
    weight: 8
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_cs1_description:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 100
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 100
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_cs1_game_1:
    type: paragraphs
    weight: 9
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_2:
    type: paragraphs
    weight: 15
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_3:
    type: paragraphs
    weight: 19
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_game_4:
    type: paragraphs
    weight: 20
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_hashtags:
    type: entity_reference_autocomplete_tags
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cs1_infographic_1:
    type: paragraphs
    weight: 10
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_2:
    type: paragraphs
    weight: 16
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_3:
    type: paragraphs
    weight: 20
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_4:
    type: paragraphs
    weight: 21
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_5:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_infographic_6:
    type: paragraphs
    weight: 23
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: infographic
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_initial_favourites:
    type: number
    weight: 33
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_initial_likes:
    type: number
    weight: 32
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_initial_shares:
    type: number
    weight: 34
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_initial_views:
    type: number
    weight: 35
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_intro_video:
    type: paragraphs
    weight: 8
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_less_1_initial_likes:
    type: number
    weight: 13
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_2_initial_likes:
    type: number
    weight: 14
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_3_initial_likes:
    type: number
    weight: 15
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_4_initial_likes:
    type: number
    weight: 17
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_5_initial_likes:
    type: number
    weight: 18
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_6_initial_likes:
    type: number
    weight: 19
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_7_initial_likes:
    type: number
    weight: 22
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_8_initial_likes:
    type: number
    weight: 23
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_less_9_initial_likes:
    type: number
    weight: 24
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_cs1_lesson_1:
    type: paragraphs
    weight: 12
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_2:
    type: paragraphs
    weight: 13
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_3:
    type: paragraphs
    weight: 14
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_4:
    type: paragraphs
    weight: 16
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_5:
    type: paragraphs
    weight: 17
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_6:
    type: paragraphs
    weight: 18
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_7:
    type: paragraphs
    weight: 21
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_8:
    type: paragraphs
    weight: 22
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_lesson_9:
    type: paragraphs
    weight: 23
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_overview_video:
    type: paragraphs
    weight: 27
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_payment_type:
    type: paragraphs
    weight: 54
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_preview_video:
    type: paragraphs
    weight: 9
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: video
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_cs1_project_task_descripti:
    type: string_textfield
    weight: 30
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 255
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_cs1_project_task_max_video:
    type: options_select
    weight: 31
    region: content
    settings: {  }
    third_party_settings: {  }
  field_cs1_project_task_preview:
    type: image_image
    weight: 32
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_cs1_project_task_preview_r:
    type: image_image
    weight: 33
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_cs1_project_task_title:
    type: string_textfield
    weight: 29
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 60
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_cs1_skills:
    type: paragraphs
    weight: 3
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      closed_mode: preview
      autocollapse: all
      closed_mode_threshold: 0
      add_mode: button
      form_display_mode: default
      default_paragraph_type: skill_course
      features:
        add_above: '0'
        collapse_edit_all: '0'
        duplicate: '0'
    third_party_settings: {  }
  field_in_app_purchase_key_so:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 11
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 35
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 35
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
hidden:
  created: true
  field_cs1_comments_count: true
  field_cs1_likes_count: true
  langcode: true
  promote: true
  sticky: true
  uid: true
