uuid: 1896faaa-a06e-459e-82d2-d841f9981d6a
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_s_date_birth
    - profile.type.student
  module:
    - datetime
id: profile.student.field_s_date_birth
field_name: field_s_date_birth
entity_type: profile
bundle: student
label: 'Birth date'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
