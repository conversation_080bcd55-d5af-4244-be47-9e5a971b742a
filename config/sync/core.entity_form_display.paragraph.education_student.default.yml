uuid: 8461481b-7d89-4108-8c8a-a3fcf6cd00d4
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.education_student.field_es_date_end
    - field.field.paragraph.education_student.field_es_date_start
    - field.field.paragraph.education_student.field_es_description
    - field.field.paragraph.education_student.field_es_name
    - paragraphs.paragraphs_type.education_student
  module:
    - datetime
    - maxlength
id: paragraph.education_student.default
targetEntityType: paragraph
bundle: education_student
mode: default
content:
  field_es_date_end:
    type: datetime_default
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_es_date_start:
    type: datetime_default
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_es_description:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 60
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_es_name:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 30
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 30
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
hidden:
  created: true
  status: true
