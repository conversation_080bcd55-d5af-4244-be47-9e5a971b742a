uuid: 6798808f-01a1-429d-9df7-086776d219b3
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_s_avatar
    - profile.type.student
  module:
    - image
id: profile.student.field_s_avatar
field_name: field_s_avatar
entity_type: profile
bundle: student
label: Avatar
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: false
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
