uuid: df845145-0bf9-4da7-bc5c-81d8352a1aaa
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_s_notification_type_push
    - profile.type.student
id: profile.student.field_s_notification_type_push
field_name: field_s_notification_type_push
entity_type: profile
bundle: student
label: 'Notification type push'
description: ''
required: true
translatable: false
default_value:
  -
    value: 1
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean
