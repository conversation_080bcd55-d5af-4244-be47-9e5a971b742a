uuid: 449413ca-4f6f-4ed5-a2cb-c0546bcf87f6
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_ws_date_end
    - paragraphs.paragraphs_type.work_student
  module:
    - datetime
id: paragraph.work_student.field_ws_date_end
field_name: field_ws_date_end
entity_type: paragraph
bundle: work_student
label: 'End date'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
