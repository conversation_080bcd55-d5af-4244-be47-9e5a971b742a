uuid: bc40388d-abe8-4fad-ad3d-1cb664500932
langcode: en
status: true
dependencies:
  config:
    - filter.format.rich_text
    - node.type.course_structure_1
    - taxonomy.vocabulary.hashtag
  module:
    - block
    - bw_vimeo
    - config
    - entity_reference_revisions
    - field_ui
    - file
    - filter
    - image
    - language
    - node
    - paragraphs
    - profile
    - raven
    - rest
    - s3fs
    - search_api
    - search_api_pantheon
    - system
    - taxonomy
    - toolbar
    - update
    - video_embed_field
    - views_ui
id: super_administrator
label: 'Super administrator'
weight: -4
is_admin: true
permissions:
  - 'access administration pages'
  - 'access content overview'
  - 'access files overview'
  - 'access profile overview'
  - 'access search_api_pantheon debug information'
  - 'access site in maintenance mode'
  - 'access site reports'
  - 'access taxonomy overview'
  - 'access toolbar'
  - 'administer account settings'
  - 'administer blocks'
  - 'administer content types'
  - 'administer display modes'
  - 'administer filters'
  - 'administer image styles'
  - 'administer languages'
  - 'administer menu'
  - 'administer modules'
  - 'administer node display'
  - 'administer node fields'
  - 'administer node form display'
  - 'administer nodes'
  - 'administer paragraph display'
  - 'administer paragraph fields'
  - 'administer paragraph form display'
  - 'administer paragraphs settings'
  - 'administer paragraphs types'
  - 'administer permissions'
  - 'administer profile'
  - 'administer profile display'
  - 'administer profile fields'
  - 'administer profile form display'
  - 'administer profile types'
  - 'administer rest resources'
  - 'administer s3fs'
  - 'administer search_api'
  - 'administer site configuration'
  - 'administer software updates'
  - 'administer taxonomy'
  - 'administer taxonomy_term display'
  - 'administer taxonomy_term fields'
  - 'administer taxonomy_term form display'
  - 'administer themes'
  - 'administer user display'
  - 'administer user fields'
  - 'administer user form display'
  - 'administer users'
  - 'administer views'
  - administer_vimeo_upload_settings
  - 'bypass node access'
  - 'create course_structure_1 content'
  - 'create terms in hashtag'
  - 'delete all revisions'
  - 'delete any course_structure_1 content'
  - 'delete course_structure_1 revisions'
  - 'delete orphan revisions'
  - 'delete own course_structure_1 content'
  - 'delete terms in hashtag'
  - 'edit any course_structure_1 content'
  - 'edit behavior plugin settings'
  - 'edit own course_structure_1 content'
  - 'edit terms in hashtag'
  - 'export configuration'
  - 'import configuration'
  - 'link to any page'
  - 'never autoplay videos'
  - 'revert all revisions'
  - 'revert course_structure_1 revisions'
  - 'select account cancellation method'
  - 'send javascript errors to sentry'
  - 'synchronize configuration'
  - upload_vimeo_videos
  - 'use text format rich_text'
  - 'view all revisions'
  - 'view any profile'
  - 'view course_structure_1 revisions'
  - 'view own profile'
  - 'view own unpublished content'
  - 'view own unpublished profile'
  - 'view the administration theme'
  - 'view unpublished paragraphs'
  - 'view update notifications'
  - 'view user email addresses'
