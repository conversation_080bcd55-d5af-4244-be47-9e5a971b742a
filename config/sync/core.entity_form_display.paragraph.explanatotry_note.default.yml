uuid: c5b4d037-b1d6-404b-8706-baf11e2a6d8a
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.explanatotry_note.field_cq_title
    - field.field.paragraph.explanatotry_note.field_description
    - field.field.paragraph.explanatotry_note.field_explanatory_note_point
    - field.field.paragraph.explanatotry_note.field_i_title
    - paragraphs.paragraphs_type.explanatotry_note
  module:
    - maxlength
id: paragraph.explanatotry_note.default
targetEntityType: paragraph
bundle: explanatotry_note
mode: default
content:
  field_cq_title:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 50
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_description:
    type: string_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_explanatory_note_point:
    type: string_textarea
    weight: 3
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_i_title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 100
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
hidden:
  created: true
  status: true
