uuid: 309c6e25-5ac7-420d-b033-47240060de26
langcode: en
status: true
dependencies:
  config:
    - field.field.node.course_structure_2.field_app_purchase_key
    - field.field.node.course_structure_2.field_app_purchase_key_dicount
    - field.field.node.course_structure_2.field_app_version
    - field.field.node.course_structure_2.field_badge
    - field.field.node.course_structure_2.field_cs1_comments_count
    - field.field.node.course_structure_2.field_cs1_description
    - field.field.node.course_structure_2.field_cs1_game_1
    - field.field.node.course_structure_2.field_cs1_game_2
    - field.field.node.course_structure_2.field_cs1_game_3
    - field.field.node.course_structure_2.field_cs1_game_4
    - field.field.node.course_structure_2.field_cs1_hashtags
    - field.field.node.course_structure_2.field_cs1_infographic_1
    - field.field.node.course_structure_2.field_cs1_infographic_2
    - field.field.node.course_structure_2.field_cs1_infographic_3
    - field.field.node.course_structure_2.field_cs1_infographic_4
    - field.field.node.course_structure_2.field_cs1_infographic_5
    - field.field.node.course_structure_2.field_cs1_infographic_6
    - field.field.node.course_structure_2.field_cs1_initial_favourites
    - field.field.node.course_structure_2.field_cs1_initial_likes
    - field.field.node.course_structure_2.field_cs1_initial_shares
    - field.field.node.course_structure_2.field_cs1_initial_views
    - field.field.node.course_structure_2.field_cs1_intro_video
    - field.field.node.course_structure_2.field_cs1_less_1_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_2_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_3_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_4_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_5_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_6_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_7_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_8_initial_likes
    - field.field.node.course_structure_2.field_cs1_less_9_initial_likes
    - field.field.node.course_structure_2.field_cs1_lesson_1
    - field.field.node.course_structure_2.field_cs1_lesson_2
    - field.field.node.course_structure_2.field_cs1_lesson_3
    - field.field.node.course_structure_2.field_cs1_lesson_4
    - field.field.node.course_structure_2.field_cs1_lesson_5
    - field.field.node.course_structure_2.field_cs1_lesson_6
    - field.field.node.course_structure_2.field_cs1_lesson_7
    - field.field.node.course_structure_2.field_cs1_lesson_8
    - field.field.node.course_structure_2.field_cs1_lesson_9
    - field.field.node.course_structure_2.field_cs1_likes_count
    - field.field.node.course_structure_2.field_cs1_overview_video
    - field.field.node.course_structure_2.field_cs1_payment_type
    - field.field.node.course_structure_2.field_cs1_preview_video
    - field.field.node.course_structure_2.field_cs1_project_task_descripti
    - field.field.node.course_structure_2.field_cs1_project_task_max_video
    - field.field.node.course_structure_2.field_cs1_project_task_preview
    - field.field.node.course_structure_2.field_cs1_project_task_preview_r
    - field.field.node.course_structure_2.field_cs1_project_task_title
    - field.field.node.course_structure_2.field_cs1_skills
    - field.field.node.course_structure_2.field_in_app_purchase_key_so
    - node.type.course_structure_2
  module:
    - entity_reference_revisions
    - field_group
    - image
    - options
    - user
third_party_settings:
  field_group:
    group_access:
      children:
        - field_cs1_payment_type
      label: Access
      parent_name: ''
      region: content
      weight: 4
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: false
        description: ''
        required_fields: false
    group_sections:
      children:
        - group_preview
        - group_section_1
        - group_s
        - group_section_3
        - group_section_4
        - group_section_5
        - group_section_6
        - group_section_7
        - group_section_8
        - group_section_9
        - group_section_10
        - group_section_11
        - group_section_12
        - group_section_13
        - group_section_14
        - group_section_15
      label: Sections
      parent_name: ''
      region: content
      weight: 5
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: vertical
        width_breakpoint: 640
    group_section_1:
      children:
        - field_cs1_intro_video
        - field_cs1_game_1
        - field_cs1_infographic_1
      label: 'Section #1'
      parent_name: group_sections
      region: content
      weight: 21
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_s:
      children:
        - field_cs1_lesson_1
      label: 'Section #2'
      parent_name: group_sections
      region: content
      weight: 22
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_3:
      children:
        - field_cs1_lesson_2
      label: 'Section #3'
      parent_name: group_sections
      region: content
      weight: 23
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_4:
      children:
        - field_cs1_lesson_3
      label: 'Section #4'
      parent_name: group_sections
      region: content
      weight: 24
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_5:
      children:
        - field_cs1_game_2
        - field_cs1_infographic_2
      label: 'Section #5'
      parent_name: group_sections
      region: content
      weight: 25
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_6:
      children:
        - field_cs1_lesson_4
      label: 'Section #6'
      parent_name: group_sections
      region: content
      weight: 26
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_7:
      children:
        - field_cs1_lesson_5
      label: 'Section #7'
      parent_name: group_sections
      region: content
      weight: 27
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_8:
      children:
        - field_cs1_lesson_6
      label: 'Section #8'
      parent_name: group_sections
      region: content
      weight: 28
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_9:
      children:
        - field_cs1_game_3
        - field_cs1_infographic_3
      label: 'Section #9'
      parent_name: group_sections
      region: content
      weight: 29
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_10:
      children:
        - field_cs1_lesson_7
      label: 'Section #10'
      parent_name: group_sections
      region: content
      weight: 31
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_11:
      children:
        - field_cs1_lesson_8
      label: 'Section #11'
      parent_name: group_sections
      region: content
      weight: 32
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_12:
      children:
        - field_cs1_lesson_9
      label: 'Section #12'
      parent_name: group_sections
      region: content
      weight: 33
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_13:
      children:
        - field_cs1_game_4
      label: 'Section #13'
      parent_name: group_sections
      region: content
      weight: 34
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_14:
      children:
        - group_course_project_task
        - field_cs1_infographic_4
        - field_cs1_infographic_5
        - field_cs1_infographic_6
      label: 'Section #14'
      parent_name: group_sections
      region: content
      weight: 35
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_section_15:
      children:
        - field_cs1_overview_video
      label: 'Section #15'
      parent_name: group_sections
      region: content
      weight: 36
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
    group_course_project_task:
      children:
        - field_cs1_project_task_title
        - field_cs1_project_task_descripti
        - field_cs1_project_task_max_video
        - field_cs1_project_task_preview
        - field_cs1_project_task_preview_r
      label: 'Course project task'
      parent_name: group_section_14
      region: content
      weight: 20
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        description: ''
    group_preview:
      children:
        - field_cs1_preview_video
      label: Preview
      parent_name: group_sections
      region: content
      weight: 20
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        formatter: closed
        description: ''
id: node.course_structure_2.default
targetEntityType: node
bundle: course_structure_2
mode: default
content:
  field_app_purchase_key:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 78
    region: content
  field_app_purchase_key_dicount:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 79
    region: content
  field_app_version:
    type: number_decimal
    label: above
    settings:
      thousand_separator: ''
      decimal_separator: .
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    weight: 81
    region: content
  field_badge:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 82
    region: content
  field_cs1_description:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_cs1_game_1:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 8
    region: content
  field_cs1_game_2:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 13
    region: content
  field_cs1_game_3:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 17
    region: content
  field_cs1_game_4:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 49
    region: content
  field_cs1_hashtags:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_cs1_infographic_1:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 9
    region: content
  field_cs1_infographic_2:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 14
    region: content
  field_cs1_infographic_3:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 18
    region: content
  field_cs1_infographic_4:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 23
    region: content
  field_cs1_infographic_5:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 24
    region: content
  field_cs1_infographic_6:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 25
    region: content
  field_cs1_intro_video:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 7
    region: content
  field_cs1_lesson_1:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 10
    region: content
  field_cs1_lesson_2:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 11
    region: content
  field_cs1_lesson_3:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 12
    region: content
  field_cs1_lesson_4:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 14
    region: content
  field_cs1_lesson_5:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 15
    region: content
  field_cs1_lesson_6:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 16
    region: content
  field_cs1_lesson_7:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 19
    region: content
  field_cs1_lesson_8:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 20
    region: content
  field_cs1_lesson_9:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 21
    region: content
  field_cs1_overview_video:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 25
    region: content
  field_cs1_payment_type:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 51
    region: content
  field_cs1_preview_video:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 6
    region: content
  field_cs1_project_task_descripti:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 9
    region: content
  field_cs1_project_task_max_video:
    type: list_default
    label: inline
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
  field_cs1_project_task_preview:
    type: image
    label: hidden
    settings:
      image_link: file
      image_style: medium
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 11
    region: content
  field_cs1_project_task_preview_r:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 12
    region: content
  field_cs1_project_task_title:
    type: string
    label: inline
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 8
    region: content
  field_cs1_skills:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_in_app_purchase_key_so:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 80
    region: content
hidden:
  field_cs1_comments_count: true
  field_cs1_initial_favourites: true
  field_cs1_initial_likes: true
  field_cs1_initial_shares: true
  field_cs1_initial_views: true
  field_cs1_less_1_initial_likes: true
  field_cs1_less_2_initial_likes: true
  field_cs1_less_3_initial_likes: true
  field_cs1_less_4_initial_likes: true
  field_cs1_less_5_initial_likes: true
  field_cs1_less_6_initial_likes: true
  field_cs1_less_7_initial_likes: true
  field_cs1_less_8_initial_likes: true
  field_cs1_less_9_initial_likes: true
  field_cs1_likes_count: true
  langcode: true
  links: true
  search_api_excerpt: true
