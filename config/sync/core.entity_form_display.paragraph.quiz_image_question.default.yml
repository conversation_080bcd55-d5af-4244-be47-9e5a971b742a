uuid: 56d0a2ae-4bc2-4502-a763-a0242919be01
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.quiz_image_question.field_iq_correct_answer_text
    - field.field.paragraph.quiz_image_question.field_iq_image
    - field.field.paragraph.quiz_image_question.field_iq_option_1_correct
    - field.field.paragraph.quiz_image_question.field_iq_option_1_title
    - field.field.paragraph.quiz_image_question.field_iq_option_2_correct
    - field.field.paragraph.quiz_image_question.field_iq_option_2_title
    - field.field.paragraph.quiz_image_question.field_iq_option_3_correct
    - field.field.paragraph.quiz_image_question.field_iq_option_3_title
    - field.field.paragraph.quiz_image_question.field_iq_question_title
    - field.field.paragraph.quiz_image_question.field_iq_wrong_answer_text
    - image.style.thumbnail
    - paragraphs.paragraphs_type.quiz_image_question
  module:
    - image
    - maxlength
id: paragraph.quiz_image_question.default
targetEntityType: paragraph
bundle: quiz_image_question
mode: default
content:
  field_iq_correct_answer_text:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 50
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_iq_image:
    type: image_image
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_iq_option_1_correct:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_iq_option_1_title:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 50
      placeholder: 'Option #1'
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_iq_option_2_correct:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_iq_option_2_title:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 50
      placeholder: 'Option #2'
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_iq_option_3_correct:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_iq_option_3_title:
    type: string_textfield
    weight: 7
    region: content
    settings:
      size: 50
      placeholder: 'Option #3'
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_iq_question_title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 75
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_iq_wrong_answer_text:
    type: string_textfield
    weight: 9
    region: content
    settings:
      size: 50
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
hidden:
  created: true
  status: true
