uuid: 92bf177c-e333-4184-902d-1e2acd487355
langcode: en
status: true
dependencies:
  config:
    - bw_trophies.trophy_type.nfr_trophy
    - field.storage.paragraph.field_trophy
    - paragraphs.paragraphs_type.reward
id: paragraph.reward.field_trophy
field_name: field_trophy
entity_type: paragraph
bundle: reward
label: Trophy
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:trophy'
  handler_settings:
    target_bundles:
      nfr_trophy: nfr_trophy
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
