uuid: 095e9416-1252-4f81-9d79-c3b8f648daf9
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.quiz_video_question.field_vq_correct_answer_text
    - field.field.paragraph.quiz_video_question.field_vq_option_1_correct
    - field.field.paragraph.quiz_video_question.field_vq_option_1_title
    - field.field.paragraph.quiz_video_question.field_vq_option_2_correct
    - field.field.paragraph.quiz_video_question.field_vq_option_2_title
    - field.field.paragraph.quiz_video_question.field_vq_question_title
    - field.field.paragraph.quiz_video_question.field_vq_video
    - field.field.paragraph.quiz_video_question.field_vq_wrong_answer_text
    - paragraphs.paragraphs_type.quiz_video_question
  module:
    - bw_vimeo
    - maxlength
id: paragraph.quiz_video_question.default
targetEntityType: paragraph
bundle: quiz_video_question
mode: default
content:
  field_vq_correct_answer_text:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 50
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_vq_option_1_correct:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_vq_option_1_title:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 12
      placeholder: 'Option #1'
    third_party_settings:
      maxlength:
        maxlength_js: 12
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_vq_option_2_correct:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_vq_option_2_title:
    type: string_textfield
    weight: 5
    region: content
    settings:
      size: 12
      placeholder: 'Option #2'
    third_party_settings:
      maxlength:
        maxlength_js: 12
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_vq_question_title:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 50
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 80
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
  field_vq_video:
    type: bw_vimeo
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_vq_wrong_answer_text:
    type: string_textfield
    weight: 9
    region: content
    settings:
      size: 50
      placeholder: ''
    third_party_settings:
      maxlength:
        maxlength_js: 50
        maxlength_js_label: 'Content limited to @limit characters, remaining: <strong>@remaining</strong>'
        maxlength_js_enforce: true
hidden:
  created: true
  status: true
