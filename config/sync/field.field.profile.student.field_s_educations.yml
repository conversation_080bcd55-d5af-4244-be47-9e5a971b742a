uuid: cf323332-9bd2-433a-a1ea-e9b40234e4b4
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_s_educations
    - paragraphs.paragraphs_type.education_student
    - profile.type.student
  module:
    - entity_reference_revisions
id: profile.student.field_s_educations
field_name: field_s_educations
entity_type: profile
bundle: student
label: Educations
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      education_student: education_student
    negate: 0
    target_bundles_drag_drop:
      access_axons:
        weight: 20
        enabled: false
      access_neurons:
        weight: 21
        enabled: false
      education_student:
        weight: 24
        enabled: true
      infographic:
        weight: 25
        enabled: false
      quiz_crossword:
        weight: 26
        enabled: false
      quiz_crossword_letter:
        weight: 27
        enabled: false
      quiz_image:
        weight: 28
        enabled: false
      quiz_image_question:
        weight: 29
        enabled: false
      quiz_tinder_mechanics:
        weight: 30
        enabled: false
      quiz_tinder_mechanics_question:
        weight: 31
        enabled: false
      quiz_video:
        weight: 32
        enabled: false
      quiz_video_question:
        weight: 33
        enabled: false
      skill_course:
        weight: 34
        enabled: false
      skill_student:
        weight: 35
        enabled: false
      video:
        weight: 37
        enabled: false
      work_student:
        weight: 38
        enabled: false
field_type: entity_reference_revisions
