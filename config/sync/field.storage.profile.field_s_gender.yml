uuid: d0205d3b-51a7-41f4-b04c-1a95e663aead
langcode: en
status: true
dependencies:
  module:
    - options
    - profile
id: profile.field_s_gender
field_name: field_s_gender
entity_type: profile
type: list_string
settings:
  allowed_values:
    -
      value: male
      label: Male
    -
      value: female
      label: Female
    -
      value: other
      label: Other
    -
      value: not_to_say
      label: 'Prefer not to say'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
