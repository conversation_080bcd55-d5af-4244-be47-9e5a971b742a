uuid: 92d3d7ea-14c8-4338-8b1e-d2124bd2b04c
langcode: en
status: true
dependencies:
  config:
    - filter.format.rich_text
    - media.type.audio
    - media.type.image
    - node.type.basic_page
    - node.type.course_structure_1
    - node.type.course_structure_2
    - node.type.course_structure_3
    - node.type.shortzzz
    - node.type.video
    - rest.resource.bw_big_five
    - rest.resource.bw_user_branch_progress
    - taxonomy.vocabulary.hashtag
  module:
    - administerusersbyrole
    - bw_misc
    - bw_trophies
    - bw_vimeo
    - config_pages
    - file
    - filter
    - media
    - media_bulk_upload
    - node
    - paragraphs_type_permissions
    - profile
    - rest
    - system
    - taxonomy
    - toolbar
    - views_bulk_edit
id: moderator
label: Moderator
weight: -6
is_admin: null
permissions:
  - 'access administration pages'
  - 'access content overview'
  - 'access media overview'
  - 'access taxonomy overview'
  - 'access toolbar'
  - 'access trophies'
  - 'access user profiles'
  - 'access users overview'
  - 'administer bw onboarding'
  - 'administer nodes'
  - 'allow empty user mail'
  - 'bypass node access'
  - 'bypass paragraphs type content access'
  - 'cancel account'
  - 'cancel users by role'
  - 'change own username'
  - 'create audio media'
  - 'create basic_page content'
  - 'create course_structure_1 content'
  - 'create course_structure_2 content'
  - 'create course_structure_3 content'
  - 'create image media'
  - 'create media'
  - 'create shortzzz content'
  - 'create terms in hashtag'
  - 'create users'
  - 'create video content'
  - 'delete all revisions'
  - 'delete any audio media'
  - 'delete any audio media revisions'
  - 'delete any basic_page content'
  - 'delete any course_structure_1 content'
  - 'delete any course_structure_2 content'
  - 'delete any course_structure_3 content'
  - 'delete any image media'
  - 'delete any image media revisions'
  - 'delete any media'
  - 'delete any shortzzz content'
  - 'delete any video content'
  - 'delete basic_page revisions'
  - 'delete course_structure_1 revisions'
  - 'delete course_structure_2 revisions'
  - 'delete course_structure_3 revisions'
  - 'delete media'
  - 'delete own audio media'
  - 'delete own basic_page content'
  - 'delete own course_structure_1 content'
  - 'delete own course_structure_2 content'
  - 'delete own course_structure_3 content'
  - 'delete own files'
  - 'delete own image media'
  - 'delete own shortzzz content'
  - 'delete own video content'
  - 'delete shortzzz revisions'
  - 'delete terms in hashtag'
  - 'delete video revisions'
  - 'edit any audio media'
  - 'edit any basic_page content'
  - 'edit any course_structure_1 content'
  - 'edit any course_structure_2 content'
  - 'edit any course_structure_3 content'
  - 'edit any image media'
  - 'edit any shortzzz content'
  - 'edit any video content'
  - 'edit config_pages entity'
  - 'edit lobby_banner config page entity'
  - 'edit offers config page entity'
  - 'edit own audio media'
  - 'edit own basic_page content'
  - 'edit own course_structure_1 content'
  - 'edit own course_structure_2 content'
  - 'edit own course_structure_3 content'
  - 'edit own image media'
  - 'edit own shortzzz content'
  - 'edit own video content'
  - 'edit recommended_courses config page entity'
  - 'edit special_offers config page entity'
  - 'edit terms in hashtag'
  - 'edit users by role'
  - 'restful get bw_big_five'
  - 'restful get bw_user_branch_progress'
  - 'revert all revisions'
  - 'revert any audio media revisions'
  - 'revert any image media revisions'
  - 'revert basic_page revisions'
  - 'revert course_structure_1 revisions'
  - 'revert course_structure_2 revisions'
  - 'revert course_structure_3 revisions'
  - 'revert shortzzz revisions'
  - 'revert video revisions'
  - 'role-assign users by role'
  - 'update any media'
  - 'update media'
  - upload_vimeo_videos
  - 'use audio_upload bulk upload form'
  - 'use avatars bulk upload form'
  - 'use text format rich_text'
  - 'use views bulk edit'
  - 'view all media revisions'
  - 'view all revisions'
  - 'view any audio media revisions'
  - 'view any image media revisions'
  - 'view any student profile'
  - 'view basic_page revisions'
  - 'view config_pages entity'
  - 'view course_structure_1 revisions'
  - 'view course_structure_2 revisions'
  - 'view course_structure_3 revisions'
  - 'view lobby_banner config page entity'
  - 'view offers config page entity'
  - 'view own unpublished content'
  - 'view own unpublished media'
  - 'view recommended_courses config page entity'
  - 'view shortzzz revisions'
  - 'view special_offers config page entity'
  - 'view user email addresses'
  - 'view users by role'
  - 'view video revisions'
