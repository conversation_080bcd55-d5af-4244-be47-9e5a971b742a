uuid: 2fe34c05-be09-4366-ba29-2d72dd3f493b
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_s_gender
    - profile.type.student
  module:
    - options
id: profile.student.field_s_gender
field_name: field_s_gender
entity_type: profile
bundle: student
label: Gender
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
