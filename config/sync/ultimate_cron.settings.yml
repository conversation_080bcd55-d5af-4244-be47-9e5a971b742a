_core:
  default_config_hash: xNvhVNcCJsZiYwYTw9TTnRv1SjLVO7H5BCK1U8Brvfo
bypass_transactional_safe_connection: false
queue:
  enabled: false
  timeouts:
    lease_time: 30.0
    time: 15.0
  delays:
    empty_delay: 0.0
    item_delay: 0.0
  throttle:
    enabled: true
    threads: 4
    threshold: 10
launcher:
  thread: any
  max_threads: 1
  lock_timeout: 3600
  max_execution_time: 3600
logger:
  cache:
    bin: ultimate_cron_logger
    timeout: -1
  database:
    method: 3
    expire: 1209600
    retain: 1000
scheduler:
  crontab:
    catch_up: 86400
    rules:
      - '*/10+@ * * * *'
  simple:
    rule: '*/15+@ * * * *'
