uuid: 6b18ac49-931d-4a9c-927e-ebd79443998e
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_lb_content_reference
    - node.type.alpha_gen
    - node.type.shortzzz
    - paragraphs.paragraphs_type.lobby_banner
id: paragraph.lobby_banner.field_lb_content_reference
field_name: field_lb_content_reference
entity_type: paragraph
bundle: lobby_banner
label: 'Content reference'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      alpha_gen: alpha_gen
      shortzzz: shortzzz
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: alpha_gen
field_type: entity_reference
