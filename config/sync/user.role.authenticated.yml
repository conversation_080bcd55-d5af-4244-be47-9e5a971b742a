uuid: 345c35d7-68ac-4377-90b3-d880923406b3
langcode: en
status: true
dependencies:
  config:
    - rest.resource.bw_alpha_gen
    - rest.resource.bw_big_five
    - rest.resource.bw_coming_soon
    - rest.resource.bw_offers
    - rest.resource.bw_onboarding
    - rest.resource.bw_scpecial_offers
    - rest.resource.bw_user_register
    - rest.resource.bw_user_smart_goals
  module:
    - file
    - media
    - paragraphs_type_permissions
    - rest
    - system
_core:
  default_config_hash: dJ0L2DNSj5q6XVZAGsuVDpJTh5UeYkIPwKrUOOpr8YI
id: authenticated
label: 'Authenticated user'
weight: -9
is_admin: false
permissions:
  - 'access content'
  - 'access user profiles'
  - 'cancel account'
  - 'change own username'
  - 'create paragraph content smart_goal'
  - 'delete own files'
  - 'restful get bw_alpha_gen'
  - 'restful get bw_big_five'
  - 'restful get bw_coming_soon'
  - 'restful get bw_offers'
  - 'restful get bw_onboarding'
  - 'restful get bw_scpecial_offers'
  - 'restful get bw_user_smart_goals'
  - 'restful patch bw_user_smart_goals'
  - 'restful post bw_user_register'
  - 'view media'
